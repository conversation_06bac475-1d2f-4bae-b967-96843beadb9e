# Incident Report: {{ incident_title }}

## Incident Overview

| Field           | Value                   |
| --------------- | ----------------------- |
| Incident Number | {{ incident_number }}   |
| Title           | {{ incident_title }}    |
| Priority        | {{ incident_priority }} |
| Severity        | {{ incident_severity }} |
| Type            | {{ incident_type }}     |
| Status          | {{ status }}            |
| Reported At     | {{ reported_at }}       |
| Reported By     | {{ reported_by }}       |
| Closed At       | {{ closed_at }}         |

## Incident Details

### Summary

{{ incident_summary }}

### Affected Services

{% for service in affected_services %}

- {{ service }}
  {% endfor %}

### Details

{{ incident_details }}

### Root Cause Analysis

{{ root_cause_analysis }}

### Tags

{% for tag in tags %}

- {{ tag }}
  {% endfor %}

### Attachments

{% for attachment in attachments %}

- [{{ attachment.name }}]({{ attachment.url }})
  {% endfor %}

## Incident Timeline

| Timestamp | Event Type | Event Name | Entity | Details |
{% for event in incident_timeline %}
| {{ event.event_datetime.strftime('%Y-%m-%d %H:%M') }} | {{ event.event_type.value }} | {{ event.event_name }} | {{ event.user || system }} | {{ event.event_details | truncate(50) }} |
{% endfor %}

## Runbook Execution

{% for runbook in runbooks %}

### {{ runbook.title }} ({{ runbook.type }})

**Purpose:** {{ runbook.purpose }}

**Steps:**
{% for step in runbook.steps %}
{{ step.step_order }}. **{{ step.title }}**

- Status: {{ step.status }}
- Executed At: {{ step.executed_at }}
- Notes: {{ step.notes }}
  {% endfor %}

{% endfor %}

## System Logs

## Resolution Summary

{{ resolution_summary }}

## Key Metrics

### Time Metrics

| Metric             | Value                    |
| ------------------ | ------------------------ |
| Time to Detection  | {{ time_to_detection }}  |
| Time to Resolution | {{ time_to_resolution }} |
| Total Downtime     | {{ total_downtime }}     |

### Impact Metrics

| Metric                   | Value                 |
| ------------------------ | --------------------- |
| Number of Affected Users | {{ affected_users }}  |
| Peak Error Rate          | {{ peak_error_rate }} |

### Financial Impact

| Metric                 | Value                |
| ---------------------- | -------------------- |
| Estimated Revenue Loss | {{ revenue_loss }}   |
| Recovery Costs         | {{ recovery_costs }} |

## Customer Communications

- Incident reported publicly? (Yes/No)
- Status page update link
- Support tickets received: {{ support_tickets_count }}
- Communications sent: {% for comm in communications %} - {{ comm }} {% endfor %}

## Retrospective

### What Went Well

{{ retrospective.what_went_well }}

### Areas for Improvement

{{ retrospective.areas_for_improvement }}

### Prevention & Recommendations

{{ retrospective.recommendations }}

## Action Items

| Description | Owner | Priority | Status |
| ----------- | ----- | -------- | ------ |

{% for action_item in action_items %}
| {{ action_item.description }} | {{ owner }} | {{ priority }} | {{ status }} |
{% endfor %}

## Audit

- Reviewed by {{ reviewed_by }}
- Reviewed on {{ reviewed_on }}
- Follow up meetings Scheduled if any
