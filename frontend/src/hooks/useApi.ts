import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { getAiAnalysis } from '../api/aiAnalysisApi';
import { getAiChatResponse } from '../api/aichatApi';
import { createIncidentEvent, getIncidentEvents } from '../api/eventsApi';
import type { IncidentMetric } from '../api/incidentApi';
import {
  createIncident,
  CreateIncidentData,
  getIncidentById,
  getIncidentDetails,
  getIncidentMetrics,
  getIncidents,
  getSimilarIncidents,
  regenerateIncidentSummary,
} from '../api/incidentApi';
import {
  getJobStatus,
  getRunningJobs,
  importFromGithub,
  syncFromGithub,
} from '../api/jobsApi';
import { fetchLabels, fetchLogs } from '../api/logsApi';
import {
  createRunbook,
  generateRunbookSteps,
  getRunbooks,
  getRunbookSteps,
  updateRunbookStep,
  UpdateRunbookStepRequest,
} from '../api/runbooksApi';
import { getUserDetails } from '../api/userApi';
import {
  IncidentEventsResponse,
  PaginationParams,
  EventType,
  EventDetails,
} from '../types/IncidentTimelineType';
import {
  Incident,
  IncidentDetails,
  IncidentList,
  SimilarIncidentsResponse,
} from '../types/IncidentType';
import { LogResponse } from '../types/logsType';
import { Runbook, RunbookStep } from '../types/RunbookType';
import { UserDetail } from '../types/UserDetailTypes';

export function queryIncidents(offset: number, limit: number) {
  return useQuery<IncidentList>({
    queryKey: ['incidents', offset, limit],
    queryFn: () => getIncidents(offset, limit),
  });
}

export function queryIncidentById(id: string) {
  return useQuery<Incident>({
    queryKey: ['incident', id],
    queryFn: () => getIncidentById(id),
  });
}

export function queryIncidentDetails(id: string) {
  return useQuery<IncidentDetails>({
    queryKey: ['incidentDetails', id],
    queryFn: () => getIncidentDetails(id),
  });
}

export function querySimilarIncidents(incidentId: string, topK: number = 5) {
  return useQuery<SimilarIncidentsResponse>({
    queryKey: ['similarIncidents', incidentId, topK],
    queryFn: () => getSimilarIncidents(incidentId, topK),
  });
}

export function queryUserDetails() {
  return useQuery<UserDetail>({
    queryKey: ['userDetails'],
    queryFn: () => getUserDetails(),
  });
}

export function queryLogs(
  query: string,
  start: string,
  end: string,
  limit = 100,
  direction: 'backward' | 'forward' = 'backward',
) {
  return useQuery<LogResponse>({
    queryKey: ['logs', query, start, end, limit, direction],
    queryFn: () => fetchLogs(query, start, end, limit, direction),
  });
}

export function useInfiniteLogs(
  query: string,
  start: string,
  end: string,
  limit = 100,
  direction: 'backward' | 'forward' = 'backward',
) {
  return useInfiniteQuery({
    queryKey: ['infinite-logs', query, start, end, limit, direction],
    queryFn: ({ pageParam }: { pageParam: string | undefined }) =>
      fetchLogs(query, start, end, limit, direction, pageParam),
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage: LogResponse) => {
      // Return the cursor for the next page if there are more logs
      return lastPage.has_more ? lastPage.next_cursor : undefined;
    },
    enabled: !!query && !!start && !!end,
  });
}

export function queryLabels(start: string, end: string, query?: string) {
  return useQuery<Record<string, string[]>>({
    queryKey: ['labels', start, end, query],
    queryFn: () => fetchLabels(start, end, query),
  });
}

export function queryRunbooks(incidentId: string) {
  return useQuery<Runbook[]>({
    queryKey: ['runbooks', incidentId],
    queryFn: () => getRunbooks(incidentId),
  });
}

export function queryRunbookSteps(incidentId: string, runbookId: string) {
  return useQuery<RunbookStep[]>({
    queryKey: ['runbookSteps', incidentId, runbookId],
    queryFn: () => getRunbookSteps(incidentId, runbookId),
  });
}
export function useGenerateRunbookSteps() {
  return useMutation({
    mutationFn: ({
      incidentId,
      runbookId,
    }: {
      incidentId: string;
      runbookId: string;
    }) => generateRunbookSteps(incidentId, runbookId),
  });
}

export function useCreateRunbook() {
  return useMutation({
    mutationFn: ({
      incidentId,
      title,
      type,
      purpose,
      details,
    }: {
      incidentId: string;
      title: string;
      type: string;
      purpose: string;
      details: string;
    }) => createRunbook(incidentId, title, type, purpose, details),
  });
}

export function useUpdateRunbookStep() {
  return useMutation({
    mutationFn: ({
      incidentId,
      runbookId,
      stepId,
      data,
    }: {
      incidentId: string;
      runbookId: string;
      stepId: string;
      data: UpdateRunbookStepRequest;
    }) => updateRunbookStep(incidentId, runbookId, stepId, data),
  });
}

export function useAiChat() {
  return useMutation({
    mutationFn: ({ message }: { message: string }) =>
      getAiChatResponse(message),
  });
}

export function useAiAnalysis(incidentId: string) {
  return useQuery({
    queryKey: ['aiAnalysis', incidentId],
    queryFn: () => getAiAnalysis(incidentId),
  });
}

export function queryIncidentEvents(
  incidentId: string,
  params?: PaginationParams,
) {
  return useQuery<IncidentEventsResponse>({
    queryKey: ['incidentEvents', incidentId, params],
    queryFn: () => getIncidentEvents(incidentId, params),
  });
}

export function useCreateIncident() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (incidentData: CreateIncidentData) =>
      createIncident(incidentData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['incidents'] });
    },
  });
}

export function useImportFromGithub() {
  return useMutation({
    mutationFn: ({
      githubRepo,
      startTime,
      endTime,
    }: {
      githubRepo: string;
      startTime?: string;
      endTime?: string;
    }) => importFromGithub(githubRepo, startTime, endTime),
    onError: (error: unknown) => {
      console.error('GitHub import failed:', error);
    },
  });
}

export function useSyncFromGithub() {
  return useMutation({
    mutationFn: ({
      githubRepo,
      startTime,
      endTime,
      incidentType = 'both',
    }: {
      githubRepo: string;
      startTime?: string;
      endTime?: string;
      incidentType?: string;
    }) => syncFromGithub(githubRepo, startTime, endTime, incidentType),
    onError: (error: unknown) => {
      console.error('GitHub sync failed:', error);
    },
  });
}

export function useRegenerateIncidentSummary() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (incidentId: string) => regenerateIncidentSummary(incidentId),
    onSuccess: (_: unknown, incidentId: string) => {
      // Invalidate both incident and incident details queries
      queryClient.invalidateQueries({ queryKey: ['incident', incidentId] });
      queryClient.invalidateQueries({
        queryKey: ['incidentDetails', incidentId],
      });
    },
  });
}

export function useJobStatus(jobId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['jobStatus', jobId],
    queryFn: () => getJobStatus(jobId),
    enabled: enabled && !!jobId,
    refetchInterval: 2000,
    refetchIntervalInBackground: true,
  });
}

export function useRunningJobs(enabled: boolean = true) {
  return useQuery({
    queryKey: ['runningJobs'],
    queryFn: () => getRunningJobs(),
    enabled: enabled,
    refetchInterval: enabled ? 5000 : false, // Only poll when enabled
    refetchIntervalInBackground: enabled,
  });
}

export function queryIncidentMetrics(incidentId: string) {
  return useQuery<IncidentMetric | null>({
    queryKey: ['incidentMetrics', incidentId],
    queryFn: () => getIncidentMetrics(incidentId),
  });
}

export function useCreateIncidentEvent(incidentId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      incidentId,
      event_name,
      event_type,
      event_details,
    }: {
      incidentId: string;
      event_name: string;
      event_type: EventType;
      event_details: EventDetails;
    }) =>
      createIncidentEvent(incidentId, event_name, event_type, event_details),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['incidentEvents', incidentId],
      });
    },
  });
}
