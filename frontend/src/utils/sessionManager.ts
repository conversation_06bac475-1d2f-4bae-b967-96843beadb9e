/**
 * Session management utilities for agent interactions
 */

/**
 * Generate a unique session ID
 * Format: session_{timestamp}_{randomId}
 */
export const generateSessionId = (): string => {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  return `session_${timestamp}_${randomId}`;
};

/**
 * Session storage key for current session
 */
const CURRENT_SESSION_KEY = 'current_agent_session_id';

/**
 * Get the current session ID from storage, or generate a new one
 */
export const getCurrentSessionId = (): string => {
  let sessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);
  
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);
  }
  
  return sessionId;
};

/**
 * Start a new session (clears the current one and creates a new one)
 */
export const startNewSession = (): string => {
  sessionStorage.removeItem(CURRENT_SESSION_KEY);
  return getCurrentSessionId();
};

/**
 * Clear the current session
 */
export const clearCurrentSession = (): void => {
  sessionStorage.removeItem(CURRENT_SESSION_KEY);
};

/**
 * Check if there's an active session
 */
export const hasActiveSession = (): boolean => {
  return sessionStorage.getItem(CURRENT_SESSION_KEY) !== null;
};
