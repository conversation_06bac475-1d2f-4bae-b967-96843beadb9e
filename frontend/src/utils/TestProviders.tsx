import React, { createContext } from 'react';
import { MemoryRouter } from 'react-router';
import { MantineProvider } from '@mantine/core';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock function for tests - ensure jest is available
const mockFn = typeof jest !== 'undefined' ? jest.fn() : () => {};

// Create a simple mock context for AuthProvider in tests
const MockAuthContext = createContext({
  isAuthenticated: true,
  user: {
    id: '1',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
  },
  token: 'mock-token',
  isLoading: false,
  login: mockFn,
  logout: mockFn,
  isLoggingIn: false,
  loginError: null,
  refetchUser: mockFn,
});

const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const mockValue = {
    isAuthenticated: true,
    user: {
      id: '1',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
    },
    token: 'mock-token',
    isLoading: false,
    login: mockFn,
    logout: mockFn,
    isLoggingIn: false,
    loginError: null,
    refetchUser: mockFn,
  };

  return (
    <MockAuthContext.Provider value={mockValue}>
      {children}
    </MockAuthContext.Provider>
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

export const TestProviders: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <MantineProvider>
        <MemoryRouter>
          <MockAuthProvider>{children}</MockAuthProvider>
        </MemoryRouter>
      </MantineProvider>
    </QueryClientProvider>
  );
};
