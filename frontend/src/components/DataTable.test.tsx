import { render, screen, fireEvent } from '../utils/test-utils';
import { DataTable } from './DataTable';

describe('DataTable component', () => {
  const mockData = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>' },
  ];

  const columns = [
    { key: 'name', header: 'Name' },
    { key: 'email', header: 'Email' },
  ];

  it('renders table headers correctly', () => {
    render(
      <DataTable
        data={mockData}
        columns={columns}
        keyExtractor={item => item.id}
      />,
    );

    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
  });

  it('renders data rows correctly', () => {
    render(
      <DataTable
        data={mockData}
        columns={columns}
        keyExtractor={item => item.id}
      />,
    );

    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles row click events', () => {
    const handleRowClick = jest.fn();

    render(
      <DataTable
        data={mockData}
        columns={columns}
        keyExtractor={item => item.id}
        onRowClick={handleRowClick}
      />,
    );

    fireEvent.click(screen.getByText('John Doe'));
    expect(handleRowClick).toHaveBeenCalledWith(mockData[0]);
  });

  it('handles server-side pagination correctly', () => {
    const handlePageChange = jest.fn();

    render(
      <DataTable
        data={mockData}
        columns={columns}
        keyExtractor={item => item.id}
        currentPage={1}
        remainingPages={3}
        onPageChange={handlePageChange}
      />,
    );

    // Click to go to next page
    fireEvent.click(screen.getByText('2'));
    expect(handlePageChange).toHaveBeenCalledWith(2);
  });

  it('renders empty rows to maintain height when data is less than itemsPerPage', () => {
    const smallData = mockData.slice(0, 1);

    render(
      <DataTable
        data={smallData}
        columns={columns}
        keyExtractor={item => item.id}
        itemsPerPage={3}
      />,
    );

    // Check if empty rows are rendered
    const rows = screen.getAllByRole('row');
    // +1 for header row, +2 for empty rows to reach itemsPerPage of 3
    expect(rows.length).toBe(4);
  });
});
