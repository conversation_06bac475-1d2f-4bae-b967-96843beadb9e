import {
  BadgeAlert,
  BookText,
  Bot,
  BrainCog,
  ChartArea,
  CircleHelp,
  FolderDot,
  ListEnd,
  ReceiptText,
  ShieldAlert,
  Users,
} from 'lucide-react';
import { useMemo } from 'react';
import { useLocation } from 'react-router';
import logo from '../../assets/logos/Logo.svg';
import { NavigationLink } from './NavigationLink';
import { TeamProfile } from './TeamProfile';

interface NavigationItem {
  link: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  matchPattern?: string;
}

const NAVIGATION_ITEMS: NavigationItem[] = [
  { link: '/agent', label: 'Abilytics AI', icon: Bot },
  { link: '/dashboard', label: 'Dashboard', icon: ShieldAlert },
  {
    link: '/incidents',
    label: 'Incidents',
    icon: BadgeAlert,
    matchPattern: '/incident',
  },
  { link: '/logs', label: 'Logs', icon: BookText },
  { link: '/metrics', label: 'Metrics', icon: ChartArea },
  { link: '/traces', label: 'Traces', icon: ListEnd },
  { link: '/knowledge-base', label: 'Knowledge Base', icon: BrainCog },
  { link: '/users', label: 'Users', icon: Users },
];

const FOOTER_ITEMS: NavigationItem[] = [
  { link: '/billing', label: 'Billing', icon: ReceiptText },
  { link: '/help', label: 'Help & Support', icon: CircleHelp },
];

export function Sidebar() {
  const location = useLocation();

  const activeItem = useMemo(() => {
    const path = location.pathname;

    // Check for exact match first
    const exactMatch = NAVIGATION_ITEMS.find(item => item.link === path);
    if (exactMatch) return exactMatch.label;

    // Check for pattern match
    const patternMatch = NAVIGATION_ITEMS.find(
      item => item.matchPattern && path.startsWith(item.matchPattern),
    );
    return patternMatch?.label;
  }, [location.pathname]);

  return (
    <nav className="h-screen w-80 bg-primary relative before:absolute before:right-0 before:top-0 before:w-[3px] before:h-full before:bg-gradient-to-b before:from-[#0184C6] before:via-[#01A578] before:to-[#A7C91F]">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <header className="p-6 flex justify-center">
          <img src={logo} alt="Abilytics" className="w-28 h-auto" />
        </header>

        {/* Projects Section */}
        <div className="px-4 mb-2">
          <NavigationLink
            to="/"
            icon={FolderDot}
            label="Projects"
            isActive={false}
            variant="projects"
          />
        </div>

        {/* Main Navigation */}
        <nav className="flex-1 px-4 space-y-1">
          {NAVIGATION_ITEMS.map(item => (
            <NavigationLink
              key={item.label}
              to={item.link}
              icon={item.icon}
              label={item.label}
              isActive={activeItem === item.label}
            />
          ))}
        </nav>

        {/* Footer Section */}
        <footer className="p-4 space-y-1">
          {FOOTER_ITEMS.map(item => (
            <NavigationLink
              key={item.label}
              to={item.link}
              icon={item.icon}
              label={item.label}
              isActive={false}
              variant="footer"
            />
          ))}

          <div className="h-px mt-6 bg-gray-700 w-full" />

          <TeamProfile />
        </footer>
      </div>
    </nav>
  );
}
