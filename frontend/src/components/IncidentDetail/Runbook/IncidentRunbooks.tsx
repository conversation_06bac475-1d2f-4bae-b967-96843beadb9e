import {
  ActionIcon,
  <PERSON>ert,
  Badge,
  Button,
  Card,
  Group,
  Paper,
  Stack,
  Tabs,
  Text,
  TextInput,
  ThemeIcon,
  Timeline,
  Title,
  Tooltip,
  Box,
} from '@mantine/core';
import {
  AlertTriangle,
  BookOpen,
  Calendar,
  ExternalLink,
  Eye,
  Plus,
  Play,
  Search,
  X,
} from 'lucide-react';
import { useDisclosure } from '@mantine/hooks';
import { useState } from 'react';
import { queryRunbooks, useGenerateRunbookSteps } from '../../../hooks/useApi';
import RunbookSteps from './RunbookSteps';
import { useQueryClient } from '@tanstack/react-query';
import CreateRunbookModal from './CreateRunbookModal';

const IncidentRunbooks = ({ incidentId }: { incidentId: string }) => {
  const queryClient = useQueryClient();
  const generateSteps = useGenerateRunbookSteps();
  const [activeRecommendedRunbook, setActiveRecommendedRunbook] = useState<
    string | null
  >(null);

  const [activeRunbook, setActiveRunbook] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { data: runbooks } = queryRunbooks(incidentId);
  const [opened, { open, close }] = useDisclosure(false);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'rollback':
        return 'red';
      case 'investigation':
        return 'blue';
      case 'mitigation':
        return 'orange';
      case 'recovery':
        return 'green';
      default:
        return 'gray';
    }
  };

  const getRecommendedRunbooks = () => {
    return runbooks?.filter(rb => rb) ?? [];
  };

  function handleGenerateMoreSteps(incidentId: string, runbookId: string) {
    generateSteps.mutate(
      { incidentId, runbookId },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ['runbookSteps', incidentId, runbookId],
          });
        },
      },
    );
  }

  const recommendedRunbooks = getRecommendedRunbooks();

  return (
    <>
      <Stack gap="lg">
        <Paper p="lg" radius="md" withBorder>
          <Group justify="space-between" align="center" mb="md">
            <Group align="center" gap="sm">
              <BookOpen size={24} color="green" />
              <Title order={2}>Runbooks & Procedures</Title>
              <Badge color="green" variant="light">
                {runbooks?.length} available
              </Badge>
            </Group>
            <Button
              leftSection={<Plus size={16} />}
              size="sm"
              variant="filled"
              onClick={open}
            >
              Create Runbook
            </Button>
          </Group>

          {/* AI Recommendations */}
          {recommendedRunbooks.length > 0 && (
            <Alert color="green" icon={<AlertTriangle size={16} />} mb="md">
              <Text size="sm">
                <strong>AI Recommendation:</strong> Based on my analysis of the
                incident and the available runbooks, I recommend starting with
                the {recommendedRunbooks[0].title} runbook.
              </Text>
            </Alert>
          )}

          <Tabs defaultValue="recommended" color="green">
            <Tabs.List>
              <Tabs.Tab value="recommended">
                Recommended ({recommendedRunbooks.length})
              </Tabs.Tab>
              <Tabs.Tab value="all">All Runbooks ({runbooks?.length})</Tabs.Tab>
              <Tabs.Tab value="history">Execution History</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="recommended" pt="md">
              <Stack gap="md">
                {recommendedRunbooks.map(runbook => (
                  <Card
                    key={runbook.id}
                    padding="lg"
                    radius="sm"
                    withBorder
                    style={{ width: '100%', overflow: 'hidden' }}
                  >
                    <Group justify="space-between" align="flex-start" mb="md">
                      <div style={{ flex: 1 }}>
                        <Group align="center" gap="sm" mb="xs">
                          <ThemeIcon
                            size="md"
                            color={getCategoryColor(runbook.type)}
                            variant="light"
                          >
                            <BookOpen size={16} />
                          </ThemeIcon>
                          <Title order={4}>{runbook.title}</Title>
                          <Badge
                            size="sm"
                            color={getCategoryColor(runbook.type)}
                            variant="light"
                          >
                            {runbook.type}
                          </Badge>
                        </Group>
                        <Text ta="left" size="sm" c="dimmed" mb="sm">
                          {runbook.purpose}
                        </Text>
                        <Group gap="sm" align="center">
                          <Text size="xs" c="dimmed">
                            Created{' '}
                            {new Date(runbook.created_at).toLocaleDateString()}
                          </Text>
                        </Group>
                      </div>
                      <Group gap="xs">
                        <Button
                          size="sm"
                          color="green"
                          leftSection={<Play size={16} />}
                          onClick={() =>
                            setActiveRecommendedRunbook(runbook.id)
                          }
                        >
                          Execute
                        </Button>
                        <Button
                          size="sm"
                          color="blue"
                          variant="light"
                          leftSection={
                            activeRecommendedRunbook === runbook.id ? (
                              <X size={16} />
                            ) : (
                              <Eye size={16} />
                            )
                          }
                          onClick={() =>
                            setActiveRecommendedRunbook(
                              activeRecommendedRunbook === runbook.id
                                ? null
                                : runbook.id,
                            )
                          }
                        >
                          {activeRecommendedRunbook === runbook.id
                            ? 'Close'
                            : 'View'}
                        </Button>
                        <ActionIcon variant="subtle" size="sm">
                          <ExternalLink size={16} />
                        </ActionIcon>
                      </Group>
                    </Group>

                    {activeRecommendedRunbook === runbook.id && (
                      <Box style={{ width: '100%', overflowX: 'auto' }}>
                        <div
                          style={{ minWidth: 'fit-content', maxWidth: '100%' }}
                        >
                          <RunbookSteps
                            incidentId={incidentId}
                            runbookId={runbook.id}
                          />
                        </div>
                        <div className="flex justify-center mt-4 mb-4">
                          <Button
                            variant="outline"
                            color="green"
                            loading={generateSteps.isPending}
                            onClick={() =>
                              handleGenerateMoreSteps(
                                runbook.incident_id,
                                runbook.id,
                              )
                            }
                          >
                            Generate More Steps
                          </Button>
                        </div>
                      </Box>
                    )}
                  </Card>
                ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="all" pt="md">
              <TextInput
                placeholder="Search runbooks by title, keywords, or tags..."
                leftSection={<Search size={16} />}
                rightSection={
                  searchQuery && (
                    <Tooltip label="Clear search">
                      <ActionIcon
                        variant="subtle"
                        color="gray"
                        size="sm"
                        onClick={() => setSearchQuery('')}
                      >
                        <X size={14} />
                      </ActionIcon>
                    </Tooltip>
                  )
                }
                value={searchQuery}
                onChange={e => setSearchQuery(e.currentTarget.value)}
                mb="md"
              />
              <Stack gap="md">
                {runbooks?.map(runbook => (
                  <Card
                    key={runbook.id}
                    padding="lg"
                    radius="sm"
                    withBorder
                    style={{ width: '100%', overflow: 'hidden' }}
                  >
                    <Group justify="space-between" align="flex-start" mb="md">
                      <div style={{ flex: 1 }}>
                        <Group align="center" gap="sm" mb="xs">
                          <ThemeIcon
                            size="md"
                            color={getCategoryColor(runbook.type)}
                            variant="light"
                          >
                            <BookOpen size={16} />
                          </ThemeIcon>
                          <Title order={4}>{runbook.title}</Title>
                          <Badge
                            size="sm"
                            color={getCategoryColor(runbook.type)}
                            variant="light"
                          >
                            {runbook.type}
                          </Badge>
                        </Group>
                        <Text ta="left" size="sm" c="dimmed" mb="sm">
                          {runbook.purpose}
                        </Text>
                        <Group gap="sm" align="center">
                          <Text size="xs" c="dimmed">
                            Created{' '}
                            {new Date(runbook.created_at).toLocaleDateString()}
                          </Text>
                        </Group>
                      </div>
                      <Group gap="xs">
                        <Button
                          size="sm"
                          color="green"
                          leftSection={<Play size={16} />}
                          onClick={() => setActiveRunbook(runbook.id)}
                        >
                          Execute
                        </Button>
                        <Button
                          size="sm"
                          color="blue"
                          variant="light"
                          leftSection={
                            activeRunbook === runbook.id ? (
                              <X size={16} />
                            ) : (
                              <Eye size={16} />
                            )
                          }
                          onClick={() =>
                            setActiveRunbook(
                              activeRunbook === runbook.id ? null : runbook.id,
                            )
                          }
                        >
                          {activeRunbook === runbook.id ? 'Close' : 'View'}
                        </Button>
                        <ActionIcon variant="subtle" size="sm">
                          <ExternalLink size={16} />
                        </ActionIcon>
                      </Group>
                    </Group>

                    {activeRunbook === runbook.id && (
                      <Box style={{ width: '100%', overflowX: 'auto' }}>
                        <div
                          style={{ minWidth: 'fit-content', maxWidth: '100%' }}
                        >
                          <RunbookSteps
                            incidentId={incidentId}
                            runbookId={runbook.id}
                          />
                        </div>
                        <div className="flex justify-center mt-4 mb-2">
                          <Button
                            variant="outline"
                            color="green"
                            loading={generateSteps.isPending}
                            onClick={() =>
                              handleGenerateMoreSteps(
                                runbook.incident_id,
                                runbook.id,
                              )
                            }
                          >
                            Generate More Steps
                          </Button>
                        </div>
                      </Box>
                    )}
                  </Card>
                ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="history" pt="md">
              <Timeline active={-1} bulletSize={24}>
                <Timeline.Item
                  bullet={
                    <ThemeIcon size={24} radius="xl" color="blue">
                      <Calendar size={14} />
                    </ThemeIcon>
                  }
                  title="No execution history"
                >
                  <Text size="sm" c="dimmed">
                    No runbooks have been executed for this incident yet.
                  </Text>
                </Timeline.Item>
              </Timeline>
            </Tabs.Panel>
          </Tabs>
        </Paper>
      </Stack>
      <CreateRunbookModal
        opened={opened}
        onClose={close}
        incidentId={incidentId}
      />
    </>
  );
};

export default IncidentRunbooks;
