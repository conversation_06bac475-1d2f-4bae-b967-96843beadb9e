import {
  Button,
  Group,
  Modal,
  Select,
  Stack,
  TextInput,
  Textarea,
} from '@mantine/core';
import { useState } from 'react';
import { z } from 'zod';
import { useCreateRunbook } from '../../../hooks/useApi';
import { useQueryClient } from '@tanstack/react-query';

interface CreateRunbookModalProps {
  opened: boolean;
  onClose: () => void;
  incidentId: string;
}

const runbookSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters'),
  type: z.string().min(1, 'Type is required'),
  purpose: z
    .string()
    .min(1, 'Purpose is required')
    .max(200, 'Purpose must be less than 200 characters'),
  details: z
    .string()
    .min(1, 'Details are required')
    .max(1000, 'Details must be less than 1000 characters'),
});

type RunbookFormValues = z.infer<typeof runbookSchema>;

interface FormErrors {
  title?: string;
  type?: string;
  purpose?: string;
  details?: string;
}

const CreateRunbookModal = ({
  opened,
  onClose,
  incidentId,
}: CreateRunbookModalProps) => {
  const queryClient = useQueryClient();
  const createRunbook = useCreateRunbook();

  const [formValues, setFormValues] = useState<RunbookFormValues>({
    title: '',
    type: '',
    purpose: '',
    details: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    try {
      runbookSchema.parse(formValues);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.errors.forEach(err => {
          const field = err.path[0] as keyof FormErrors;
          newErrors[field] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    createRunbook.mutate(
      {
        incidentId,
        title: formValues.title,
        type: formValues.type,
        purpose: formValues.purpose,
        details: formValues.details,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ['runbooks'],
          });
          resetForm();
          onClose();
        },
      },
    );
  };

  const resetForm = () => {
    setFormValues({
      title: '',
      type: '',
      purpose: '',
      details: '',
    });
    setErrors({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const updateField = (field: keyof RunbookFormValues, value: string) => {
    setFormValues(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      centered
      withCloseButton={false}
      title="Create Runbook"
      size="lg"
      overlayProps={{ blur: 2 }}
    >
      <form onSubmit={handleSubmit}>
        <Stack>
          <TextInput
            label="Title"
            placeholder="Enter runbook title"
            required
            value={formValues.title}
            onChange={e => updateField('title', e.target.value)}
            error={errors.title}
          />
          <Select
            label="Type"
            placeholder="Select type"
            required
            data={[
              'Troubleshoot',
              'Recovery',
              'Mitigation',
              'Rollback',
              'Other',
            ]}
            value={formValues.type}
            onChange={value => updateField('type', value || '')}
            error={errors.type}
          />
          <TextInput
            label="Purpose"
            placeholder="Enter runbook purpose"
            required
            value={formValues.purpose}
            onChange={e => updateField('purpose', e.target.value)}
            error={errors.purpose}
          />
          <Textarea
            label="Details"
            placeholder="Enter runbook details"
            required
            value={formValues.details}
            onChange={e => updateField('details', e.target.value)}
            error={errors.details}
          />
          <Group justify="flex-end">
            <Button
              type="button"
              mt="md"
              variant="outline"
              color="gray"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              mt="md"
              color="green"
              loading={createRunbook.isPending}
            >
              Create Runbook
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default CreateRunbookModal;
