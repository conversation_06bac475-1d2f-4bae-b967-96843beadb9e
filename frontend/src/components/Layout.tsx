import React, { ReactNode } from 'react';
import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';

interface LayoutProps {
  children: ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <>
      <Sidebar />
      <div className="h-full w-full flex flex-col p-0 relative">
        <Navbar />
        <main
          className="w-full h-full pt-[90px] px-10 pb-10 overflow-y-auto"
          role="main"
          aria-label="Main content"
        >
          {children}
        </main>
      </div>
    </>
  );
};
