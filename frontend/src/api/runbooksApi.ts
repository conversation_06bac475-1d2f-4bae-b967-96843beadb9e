import { Runbook, RunbookStep } from '../types/RunbookType';
import { StepStatus } from '../constants/types';
import { apiClient } from '../utils/apiClient';

// ============================================================================
// RUNBOOK MANAGEMENT
// ============================================================================

/**
 * Get all runbooks for an incident
 */
export const getRunbooks = async (incidentId: string): Promise<Runbook[]> => {
  const runbooks = await apiClient.get<any[]>(
    `/incidents/${incidentId}/runbooks`,
  );

  const data: Runbook[] = runbooks.map((runbook: any) => ({
    title: runbook.title,
    type: runbook.type,
    purpose: runbook.purpose,
    details: runbook.details,
    steps: runbook.steps,
    id: runbook.id,
    incident_id: runbook.incident_id,
    created_at: runbook.created_at,
  }));
  return data;
};

/**
 * Create a new runbook for an incident
 */
export const createRunbook = async (
  incidentId: string,
  title: string,
  type: string,
  purpose: string,
  details: string,
) => {
  try {
    return await apiClient.post(`/incidents/${incidentId}/runbooks`, {
      title: title.toLowerCase(),
      type: type.toLowerCase(),
      purpose: purpose.toLowerCase(),
      details: details.toLowerCase(),
      steps: [],
    });
  } catch (error) {
    console.error('Error creating runbook:', error);
    throw error;
  }
};

// ============================================================================
// RUNBOOK STEPS MANAGEMENT
// ============================================================================

/**
 * Get all steps for a specific runbook
 */
export const getRunbookSteps = async (
  incidentId: string,
  runbookId: string,
): Promise<RunbookStep[]> => {
  const steps = await apiClient.get<any[]>(
    `/incidents/${incidentId}/runbooks/${runbookId}/steps`,
  );

  const data: RunbookStep[] = steps.map((step: any) => ({
    step_order: step.step_order,
    id: step.id,
    title: step.title,
    description: step.description,
    details: step.details,
    notes: step.notes,
    executed_by_user: step.executed_by_user,
    executed_at: step.executed_at,
    status: step.status,
    expected_result: step.expected_result,
    runbook_id: step.runbook_id,
    created_at: step.created_at,
  }));
  return data;
};

/**
 * Generate AI-powered steps for a runbook
 */
export const generateRunbookSteps = async (
  incidentId: string,
  runbookId: string,
) => {
  await apiClient.post(
    `/incidents/${incidentId}/runbooks/${runbookId}/generate_steps`,
  );
  return 'success';
};

/**
 * Update a specific runbook step
 */
export interface UpdateRunbookStepRequest {
  status?: StepStatus;
  notes?: string;
}

export const updateRunbookStep = async (
  incidentId: string,
  runbookId: string,
  stepId: string,
  data: UpdateRunbookStepRequest,
) => {
  return await apiClient.put(
    `/incidents/${incidentId}/runbooks/${runbookId}/steps/${stepId}`,
    data,
  );
};

// ============================================================================
// EXPORT ALL RUNBOOK FUNCTIONALITY
// ============================================================================

export const runbookApi = {
  // Runbook management
  getRunbooks,
  createRunbook,

  // Steps management
  getRunbookSteps,
  generateRunbookSteps,
  updateRunbookStep,
};

export default runbookApi;
