import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Button, Textarea } from '@mantine/core';
import { useState } from 'react';
import { Controller, ControllerRenderProps, useForm } from 'react-hook-form';
import { z } from 'zod';

const knowledgeBaseSchema = z.object({
  projectDescription: z.string().min(1, 'Project description is required'),
  aimsAndScope: z.string().min(1, 'Project aims and scope is required'),
  systemArchitecture: z.string().min(1, 'System architecture is required'),
});

type KnowledgeBaseData = z.infer<typeof knowledgeBaseSchema>;

export default function KnowledgeBase() {
  const [isSaving, setIsSaving] = useState(false);

  const { control, handleSubmit, formState, reset } =
    useForm<KnowledgeBaseData>({
      resolver: zodResolver(knowledgeBaseSchema),
      defaultValues: {
        projectDescription: '',
        aimsAndScope: '',
        systemArchitecture: '',
      },
    });
  const { errors } = formState;

  const onSubmit = async (data: KnowledgeBaseData) => {
    try {
      setIsSaving(true);

      console.log('Saving knowledge base data:', data);

      await new Promise(resolve => setTimeout(resolve, 1000));

      alert('Knowledge base data saved successfully!');
    } catch (error) {
      console.error('Error saving knowledge base data:', error);
      alert('Failed to save knowledge base data');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-4xl font-semibold text-primary">Knowledge Base</h2>
      </div>
      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-secondary">
          The knowledge base is a living document that captures all the
          important details about the project. It serves as a central repository
          for information that is relevant to the project, including the project
          description, aims and scope, system architecture, and any other
          relevant information.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <div className="grid grid-cols-2 gap-6 mb-6">
          <div className="border border-gray-300 rounded-md overflow-hidden p-2 h-[200px] bg-white">
            <Controller
              name="projectDescription"
              control={control}
              render={({
                field,
              }: {
                field: ControllerRenderProps<
                  KnowledgeBaseData,
                  'projectDescription'
                >;
              }) => (
                <Textarea
                  {...field}
                  label="Project Description"
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  placeholder="Explain the project in detail. Include the problem statement, the target users, and any other relevant information."
                  minRows={12}
                  className="w-full border-0"
                  styles={{
                    root: { height: '100%' },
                    wrapper: { height: '100%', border: 'none' },
                    input: {
                      height: '100%',
                      padding: '1rem',
                      border: 'none',
                      borderRadius: 0,
                      fontSize: '1rem',
                    },
                  }}
                  error={errors.projectDescription?.message}
                />
              )}
            />
          </div>
          <div className="border border-gray-300 rounded-md overflow-hidden p-2 h-[200px] bg-white">
            <Controller
              name="aimsAndScope"
              control={control}
              render={({
                field,
              }: {
                field: ControllerRenderProps<KnowledgeBaseData, 'aimsAndScope'>;
              }) => (
                <Textarea
                  {...field}
                  label="Project Aims & Scope"
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  placeholder="Explain the aims and scope of the project in detail."
                  minRows={12}
                  className="w-full border-0"
                  styles={{
                    root: { height: '100%' },
                    wrapper: { height: '100%', border: 'none' },
                    input: {
                      height: '100%',
                      padding: '1rem',
                      border: 'none',
                      borderRadius: 0,
                      fontSize: '1rem',
                    },
                  }}
                  error={errors.aimsAndScope?.message}
                />
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div className="border border-gray-300 rounded-md overflow-hidden p-2 h-[200px] bg-white">
            <Controller
              name="systemArchitecture"
              control={control}
              render={({
                field,
              }: {
                field: ControllerRenderProps<
                  KnowledgeBaseData,
                  'systemArchitecture'
                >;
              }) => (
                <Textarea
                  {...field}
                  label="System Architecture"
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  placeholder="Explain the system architecture in detail. Include all the components, their interactions, and any relevant technical details."
                  minRows={12}
                  className="w-full border-0"
                  styles={{
                    root: { height: '100%' },
                    wrapper: { height: '100%', border: 'none' },
                    input: {
                      height: '100%',
                      padding: '1rem',
                      border: 'none',
                      borderRadius: 0,
                      fontSize: '1rem',
                    },
                  }}
                  error={errors.systemArchitecture?.message}
                />
              )}
            />
          </div>

          <div className="border border-gray-300 rounded-md overflow-hidden p-2 h-[200px] bg-white flex flex-col">
            <h3 className="font-semibold text-sm text-primary">
              Architecture Overview
            </h3>
            <div className="flex-1 flex items-center justify-center">
              <Button className="text-gray-500" disabled title="Coming soon">
                Coming Soon..
              </Button>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 space-x-4">
          <Button
            type="button"
            onClick={() => {
              console.log('Resetting form');
              reset();
            }}
            disabled={isSaving || !formState.isDirty}
          >
            Reset
          </Button>
          <Button
            type="submit"
            style={{ backgroundColor: 'var(--color-primary)' }}
            loading={isSaving}
            disabled={isSaving || !formState.isDirty}
          >
            Save
          </Button>
        </div>
      </form>
    </div>
  );
}
