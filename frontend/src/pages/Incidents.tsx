import {
  ActionIcon,
  Alert,
  Avatar,
  Badge,
  Box,
  Button,
  Container,
  Flex,
  Group,
  LoadingOverlay,
  MultiSelect,
  Popover,
  rem,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import { AlertCircle, Filter, Import, Plus, Search, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import { Column, DataTable } from '../components/DataTable';
import ImportIncidentsModal from '../components/ImportIncidentsModal';
import LinkedRepositoryModal from '../components/LinkedRepositoryModal';
import { getAvatar } from '../constants/avatars';
import { SEVERITY_COLORS, STATUS_COLORS, UI_COLORS } from '../constants/colors';
import { SEVERITY, STATUS } from '../constants/types';
import { queryIncidents } from '../hooks/useApi';
import { IncidentBase } from '../types/IncidentType';
import { Job } from '../types/JobType';

export default function Incidents() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const [searchQuery, setSearchQuery] = useState(
    searchParams.get('query') || '',
  );
  const [selectedFilters, setSelectedFilters] = useState<string[]>(
    searchParams.getAll('filter') || [],
  );
  const [page, setPage] = useState(Number(searchParams.get('page')) || 1);
  const [filterOpened, setFilterOpened] = useState(false);
  const [importModalOpened, setImportModalOpened] = useState(false);
  const [linkedRepoModalOpened, setLinkedRepoModalOpened] = useState(false);
  const [linkedRepository, setLinkedRepository] = useState<string | null>(null);

  // API hooks
  const itemsPerPage = 10;
  const { data, isLoading, error } = queryIncidents(
    (page - 1) * itemsPerPage,
    itemsPerPage,
  );
  const incidentList = data?.items || [];
  const totalPages = data?.pages || 0;

  // Update URL when filters, search, or page changes
  useEffect(() => {
    const newParams = new URLSearchParams();
    if (searchQuery) newParams.set('query', searchQuery);
    if (page > 1) newParams.set('page', String(page));
    selectedFilters.forEach(f => newParams.append('filter', f));
    setSearchParams(newParams);
  }, [searchQuery, selectedFilters, page, setSearchParams]);

  // Check for linked repository on component mount
  useEffect(() => {
    const linkedRepo = localStorage.getItem('linkedRepo');
    if (linkedRepo) {
      setLinkedRepository(linkedRepo);
    }
  }, []);

  // Filter data based on search query and filters
  const filteredData = incidentList.filter((incident: IncidentBase) => {
    // Check if incident matches search query
    const matchesSearch =
      !searchQuery ||
      Object.entries(incident).some(([key, value]) => {
        if (key === 'reporter' && typeof value === 'object') {
          const reporter = value as { first_name: string; last_name: string };
          return `${reporter.first_name} ${reporter.last_name}`
            .toLowerCase()
            .includes(searchQuery.toLowerCase());
        }
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchQuery.toLowerCase());
        }
        return false;
      });

    // Check if incident matches selected filters
    const matchesFilters =
      selectedFilters.length === 0 ||
      (() => {
        // Group filters by type
        const severityFilters = selectedFilters.filter(f =>
          [
            SEVERITY.CRITICAL,
            SEVERITY.HIGH,
            SEVERITY.MEDIUM,
            SEVERITY.LOW,
          ].includes(f.toLowerCase()),
        );
        const statusFilters = selectedFilters.filter(f =>
          [STATUS.OPEN, STATUS.ACTIVE, STATUS.RESOLVED, STATUS.CLOSED].includes(
            f.toLowerCase(),
          ),
        );

        // If no filters of a type are selected, that type is considered a match
        const matchesSeverity =
          severityFilters.length === 0 ||
          severityFilters.some(
            filter => incident.severity.toLowerCase() === filter.toLowerCase(),
          );

        const matchesStatus =
          statusFilters.length === 0 ||
          statusFilters.some(
            filter => incident.status.toLowerCase() === filter.toLowerCase(),
          );

        // Incident must match both filter types (if any are selected)
        return matchesSeverity && matchesStatus;
      })();

    return matchesSearch && matchesFilters;
  });

  const columns: Column<IncidentBase>[] = [
    {
      key: 'id',
      header: 'Incident No',
      render: incident => (
        <Text fw={600} size="sm">
          {incident.incident_number}
        </Text>
      ),
      width: '10%',
    },
    {
      key: 'name',
      header: 'Incidents',
      render: incident => (
        <Stack gap="xs">
          <Text size="sm">{incident.title}</Text>
          <Box maw={600}>
            <Text size="xs" c="dimmed" truncate="end">
              {incident.summary || 'No summary'}
            </Text>
          </Box>
        </Stack>
      ),
      width: '20%',
    },
    {
      key: 'priority',
      header: 'Priority',
      render: incident => (
        <Badge variant="light" color={UI_COLORS.MUTED} size="sm">
          {incident.priority}
        </Badge>
      ),
    },
    {
      key: 'severity',
      header: 'Severity',
      render: incident => (
        <Badge
          variant="light"
          color={getSeverityColor(incident.severity)}
          size="sm"
        >
          {incident.severity}
        </Badge>
      ),
    },
    {
      key: 'reportedTime',
      header: 'Reported Time',
      render: incident => (
        <Text fw={500} size="sm">
          {dayjs(incident.reported_at).format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: incident => (
        <Badge variant="transparent" color={getStatusColor(incident.status)}>
          {incident.status}
        </Badge>
      ),
    },
    {
      key: 'reporter',
      header: 'Reported By',
      render: incident => (
        <Group gap="sm">
          <Avatar
            size={28}
            src={
              incident.reporter?.avatar ||
              getAvatar(
                `${incident.reporter?.first_name}+${incident.reporter?.last_name}`,
              )
            }
            radius="xl"
          />
          <Text size="sm" fw={500}>
            {incident.reporter?.first_name} {incident.reporter?.last_name}
          </Text>
        </Group>
      ),
    },
  ];

  const handleRowClick = (incident: IncidentBase) => {
    navigate(`/incident/${incident.id}`);
  };

  const handleCreateIncident = () => {
    navigate('/incident/create');
  };

  const handleUnlinkRepository = useCallback(() => {
    setLinkedRepository(null);
    localStorage.removeItem('linkedRepo');
    setLinkedRepoModalOpened(false);
  }, []);

  const handleImportStarted = useCallback((_job: Job, repoUrl: string) => {
    // Update state and localStorage
    setLinkedRepository(repoUrl);
    localStorage.setItem('linkedRepo', repoUrl);

    // Close import modal and open linked repo modal to show progress
    setImportModalOpened(false);
    setLinkedRepoModalOpened(true);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case SEVERITY.CRITICAL:
        return SEVERITY_COLORS.CRITICAL.FILLED;
      case SEVERITY.HIGH:
        return SEVERITY_COLORS.HIGH.FILLED;
      case SEVERITY.MEDIUM:
        return SEVERITY_COLORS.MEDIUM.FILLED;
      case SEVERITY.LOW:
        return SEVERITY_COLORS.LOW.FILLED;
      default:
        return UI_COLORS.MUTED;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case STATUS.OPEN:
        return STATUS_COLORS.OPEN.FILLED;
      case STATUS.ACTIVE:
        return STATUS_COLORS.ACTIVE.FILLED;
      case STATUS.RESOLVED:
        return STATUS_COLORS.RESOLVED.FILLED;
      case STATUS.CLOSED:
        return STATUS_COLORS.CLOSED.FILLED;
      default:
        return UI_COLORS.MUTED;
    }
  };

  return (
    <Container fluid className="w-full">
      <Stack gap="xl">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <Title order={1} size={rem(36)} fw={600} c="var(--color-primary)">
            Incidents
          </Title>
        </Flex>

        <Stack gap="md" pos="relative">
          {/* Search & Actions */}
          <Flex justify="space-between" align="center" gap="md" wrap="wrap">
            <TextInput
              placeholder="Search incidents..."
              leftSection={<Search size={16} />}
              rightSection={
                searchQuery && (
                  <Tooltip label="Clear search">
                    <ActionIcon
                      variant="subtle"
                      color="gray"
                      size="sm"
                      onClick={() => setSearchQuery('')}
                    >
                      <X size={14} />
                    </ActionIcon>
                  </Tooltip>
                )
              }
              flex={1}
              miw={300}
              radius="md"
              value={searchQuery}
              onChange={e => {
                setPage(1);
                setSearchQuery(e.currentTarget.value);
              }}
              disabled={isLoading}
            />
            <Group gap="sm">
              <Tooltip
                label={
                  linkedRepository
                    ? `Manage repository: ${linkedRepository}`
                    : 'Import incidents'
                }
              >
                <Button
                  leftSection={<Import size={16} />}
                  variant="outline"
                  color={UI_COLORS.PRIMARY}
                  onClick={() => {
                    if (linkedRepository) {
                      setLinkedRepoModalOpened(true);
                    } else {
                      setImportModalOpened(true);
                    }
                  }}
                  disabled={isLoading}
                  style={{ minWidth: '140px' }} // Consistent sizing
                >
                  Import/Sync
                </Button>
              </Tooltip>
              <Tooltip label="Report a new incident">
                <Button
                  leftSection={<Plus size={16} />}
                  variant="filled"
                  color={UI_COLORS.PRIMARY}
                  onClick={handleCreateIncident}
                  disabled={isLoading}
                >
                  Create Incident
                </Button>
              </Tooltip>

              <Popover
                opened={filterOpened}
                onChange={setFilterOpened}
                position="bottom-end"
                shadow="md"
                withArrow
              >
                <Popover.Target>
                  <Tooltip label="Filter incidents">
                    <ActionIcon
                      variant="outline"
                      color={UI_COLORS.PRIMARY}
                      size="lg"
                      onClick={() => setFilterOpened(o => !o)}
                      disabled={isLoading}
                    >
                      <Filter size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Popover.Target>
                <Popover.Dropdown>
                  <Stack gap="md" w={320}>
                    <Text size="sm" fw={500}>
                      Filter Options
                    </Text>
                    <MultiSelect
                      data={[
                        {
                          group: 'Severity',
                          items: [
                            { value: SEVERITY.CRITICAL, label: 'Critical' },
                            { value: SEVERITY.HIGH, label: 'High' },
                            { value: SEVERITY.MEDIUM, label: 'Medium' },
                            { value: SEVERITY.LOW, label: 'Low' },
                          ],
                        },
                        {
                          group: 'Status',
                          items: [
                            { value: STATUS.OPEN, label: 'Open' },
                            { value: STATUS.ACTIVE, label: 'Active' },
                            { value: STATUS.RESOLVED, label: 'Resolved' },
                            { value: STATUS.CLOSED, label: 'Closed' },
                          ],
                        },
                      ]}
                      placeholder="Filter by severity & status"
                      radius="md"
                      value={selectedFilters}
                      onChange={values => {
                        setPage(1);
                        setSelectedFilters(values);
                      }}
                      clearable
                      searchable
                      disabled={isLoading}
                    />
                  </Stack>
                </Popover.Dropdown>
              </Popover>
            </Group>
          </Flex>

          {/* Active Filters */}
          {selectedFilters.length > 0 && (
            <Flex gap="xs" mt="sm" align="center">
              <Text size="sm" c="dimmed">
                Active filters:
              </Text>
              {selectedFilters.map(filter => (
                <Badge
                  key={filter}
                  variant="light"
                  color={UI_COLORS.INFO}
                  size="sm"
                  style={{ cursor: 'pointer' }}
                  onClick={() =>
                    setSelectedFilters(prev => prev.filter(f => f !== filter))
                  }
                >
                  {filter} ×
                </Badge>
              ))}
              <Button
                variant="subtle"
                size="xs"
                onClick={() => setSelectedFilters([])}
              >
                Clear all
              </Button>
            </Flex>
          )}

          {/* Loader */}
          <LoadingOverlay
            visible={isLoading}
            zIndex={1000}
            overlayProps={{ radius: 'sm', blur: 2 }}
          />

          {/* Error */}
          {error && (
            <Alert
              icon={<AlertCircle size={16} />}
              title="Error"
              color={UI_COLORS.ERROR}
              variant="filled"
            >
              Failed to load incidents. Please try later.
            </Alert>
          )}

          {/* Data Table */}
          {!isLoading && !error && (
            <DataTable
              data={filteredData}
              columns={columns}
              itemsPerPage={itemsPerPage}
              currentPage={page}
              remainingPages={totalPages}
              onPageChange={newPage => {
                setPage(newPage);
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
              onRowClick={handleRowClick}
              keyExtractor={(incident: IncidentBase) => incident.id}
            />
          )}
        </Stack>
        <ImportIncidentsModal
          opened={importModalOpened}
          onClose={() => setImportModalOpened(false)}
          onImportStarted={handleImportStarted}
        />
        {linkedRepository && (
          <LinkedRepositoryModal
            opened={linkedRepoModalOpened}
            onClose={() => setLinkedRepoModalOpened(false)}
            repoUrl={linkedRepository}
            onUnlink={handleUnlinkRepository}
          />
        )}
      </Stack>
    </Container>
  );
}
