import { zodResolver } from '@hookform/resolvers/zod';
import {
  Box,
  Button,
  Container,
  Divider,
  FileInput,
  Paper,
  Pill,
  PillsInput,
  Select,
  Stack,
  Textarea,
  TextInput,
} from '@mantine/core';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router';
import { z } from 'zod';
import BreadcrumbNavigation from '../components/BreadcrumbNavigation';
import { useCreateIncident } from '../hooks/useApi';
import { generateCreateIncidentBreadcrumbs } from '../utils/breadcrumbUtils';

// Define schema using zod
const incidentSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  details: z.string().optional(),
  priority: z.enum(['Critical', 'High', 'Medium', 'Low'], {
    required_error: 'Priority is required',
  }),
  type: z.enum(['Outage', 'Degradation', 'Security', 'Performance', 'Other'], {
    required_error: 'Incident type is required',
  }),
  service: z.string().min(1, 'Service is required'),
  tags: z.array(z.string()).optional(),
  actualBehavior: z.string().optional(),
  expectedBehavior: z.string().optional(),
  possibleCause: z.string().optional(),
  suggestedFix: z.string().optional(),
  additionalContext: z.string().optional(),
});

type IncidentFormData = z.infer<typeof incidentSchema>;

const tags = ['React', 'Frontend', 'API', 'Database'];
const serviceOptions = ['Service 1', 'Service 2', 'Service 3'];

// Function to combine all detail fields into incident_details string
const generateIncidentDetails = (data: IncidentFormData): string => {
  const details: string[] = [];

  // Add main details if provided
  if (data.details?.trim()) {
    details.push(`Details: ${data.details.trim()}`);
  }

  // Add actual behavior
  if (data.actualBehavior?.trim()) {
    details.push(`Actual Behavior: ${data.actualBehavior.trim()}`);
  }

  // Add expected behavior
  if (data.expectedBehavior?.trim()) {
    details.push(`Expected Behavior: ${data.expectedBehavior.trim()}`);
  }

  // Add possible cause
  if (data.possibleCause?.trim()) {
    details.push(`Possible Cause: ${data.possibleCause.trim()}`);
  }

  // Add suggested fix
  if (data.suggestedFix?.trim()) {
    details.push(`Suggested Fix: ${data.suggestedFix.trim()}`);
  }

  // Add additional context
  if (data.additionalContext?.trim()) {
    details.push(`Additional Context: ${data.additionalContext.trim()}`);
  }

  return details.join('\n\n');
};

const CreateIncident = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const incidentData = state?.incidentData;
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const createIncidentMutation = useCreateIncident();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
    setValue,
  } = useForm<IncidentFormData>({
    resolver: zodResolver(incidentSchema),
    defaultValues: {
      title: '',
      details: '',
      priority: undefined,
      type: undefined,
      service: undefined,
      tags: [],
    },
  });

  // Pre-fill form if data is passed from logs
  useEffect(() => {
    if (incidentData) {
      setValue('title', incidentData.title || '');
      setValue('details', incidentData.details || '');
      if (incidentData.services?.[0]) {
        setValue('service', incidentData.services[0]);
      }
    }
  }, [incidentData, setValue]);

  const onSubmit = async (data: IncidentFormData) => {
    try {
      // Combine all detail fields into incident_details
      const incident_details = generateIncidentDetails(data);

      // Prepare data for API
      const apiData = {
        title: data.title,
        priority: data.priority,
        severity: 'low',
        incident_type: data.type,
        affected_services: data.service ? [data.service] : [],
        tags: selectedTags,
        incident_details,
        attachments: [],
      };

      console.log('Creating Incident:', apiData);

      const createdIncident = await createIncidentMutation.mutateAsync(apiData);

      console.log('Incident created successfully:', createdIncident);

      // Navigate to the created incident or incidents list
      navigate(`/incident/${createdIncident.id}`);
    } catch (error) {
      console.error('Error creating incident:', error);
    }
  };

  return (
    <Container fluid p="md">
      <Paper p="lg" radius="md" withBorder shadow="sm">
        <Stack gap="md">
          {/* Breadcrumbs */}
          <BreadcrumbNavigation items={generateCreateIncidentBreadcrumbs()} />

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col items-start"
          >
            <span className="text-4xl font-semibold mb-[24px] text-primary">
              New Incident
            </span>

            <div className="flex justify-between w-full gap-[29px] mb-[29px]">
              <Box className="w-full text-left">
                <TextInput
                  label="Incident Title"
                  placeholder="New Incident"
                  {...register('title')}
                  error={errors.title?.message}
                  labelProps={{
                    className: 'font-semibold text-xl text-primary',
                  }}
                />
              </Box>

              <Box className="flex w-full justify-between gap-[29px]">
                <Box className="flex-1 text-left">
                  <Controller
                    control={control}
                    name="priority"
                    render={({ field }) => (
                      <Select
                        label="Priority"
                        placeholder="Select priority"
                        data={['Critical', 'High', 'Medium', 'Low']}
                        error={errors.priority?.message}
                        {...field}
                        labelProps={{
                          className: 'font-semibold text-xl text-primary',
                        }}
                      />
                    )}
                  />
                </Box>

                <Box className="flex-1 text-left">
                  <Controller
                    control={control}
                    name="type"
                    render={({ field }) => (
                      <Select
                        label="Incident Type"
                        placeholder="Select incident type"
                        data={[
                          'Outage',
                          'Degradation',
                          'Security',
                          'Performance',
                          'Other',
                        ]}
                        error={errors.type?.message}
                        {...field}
                        labelProps={{
                          className: 'font-semibold text-xl text-primary',
                        }}
                      />
                    )}
                  />
                </Box>
              </Box>
            </div>

            <div className="flex justify-between w-full gap-[29px] mb-[24px]">
              <Box className="w-full text-left">
                <Textarea
                  label="Incident Details"
                  placeholder="Describe the incident in detail"
                  {...register('details')}
                  error={errors.details?.message}
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  minRows={6}
                  autosize
                />
              </Box>

              <Box className="w-full flex flex-col items-start gap-[20px] text-left">
                <Controller
                  control={control}
                  name="service"
                  render={({ field }) => (
                    <Select
                      label="Affected Services"
                      placeholder="Select affected services"
                      data={serviceOptions}
                      error={errors.service?.message}
                      {...field}
                      labelProps={{ className: 'font-semibold text-primary' }}
                      className="w-full"
                    />
                  )}
                />

                <Box className="flex flex-col gap-2 mt-5 w-full">
                  <PillsInput
                    label="Tags"
                    labelProps={{ className: 'font-semibold text-primary' }}
                    variant="unstyled"
                  >
                    <Pill.Group>
                      {tags.map((tag, index) => (
                        <Pill
                          key={index}
                          onClick={() => {
                            setSelectedTags(prev => {
                              if (prev.includes(tag)) {
                                return prev.filter(t => t !== tag);
                              } else {
                                return [...prev, tag];
                              }
                            });
                          }}
                        >
                          {tag}
                        </Pill>
                      ))}
                      <PillsInput.Field placeholder="+ Add tag" />
                    </Pill.Group>
                  </PillsInput>
                </Box>
              </Box>
            </div>

            <Divider size="sm" variant="dashed" className="w-full mb-[24px]" />

            <div className="flex justify-between w-full gap-[29px] mb-[24px]">
              <Box className="w-full text-left">
                <Textarea
                  label="Actual Behavior"
                  placeholder="What went wrong?"
                  {...register('actualBehavior')}
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  minRows={4}
                  autosize
                />
                <Textarea
                  label="Expected Behavior"
                  placeholder="What should have happened?"
                  {...register('expectedBehavior')}
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  minRows={4}
                  autosize
                />
              </Box>

              <Box className="w-full flex flex-col items-start gap-[20px] text-left">
                <Box className="w-full flex flex-col flex-1 items-start gap-1 pointer-events-none">
                  <span className="font-semibold text-sm text-primary">
                    Attachments
                  </span>
                  <Box className="w-full border border-dashed border-[var(--color-primary)] rounded p-4 bg-gray-50 flex flex-col items-center justify-center">
                    <span className="text-gray-500 mb-32">
                      Your attachment previews will appear here
                    </span>
                    <FileInput disabled placeholder="Upload (Coming Soon)" />
                  </Box>
                </Box>
              </Box>
            </div>

            <div className="flex justify-between w-full gap-[29px] mb-[24px]">
              <Box className="w-full text-left">
                <Textarea
                  label="Possible Cause"
                  placeholder="What could have caused the issue?"
                  {...register('possibleCause')}
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  minRows={4}
                  autosize
                />
              </Box>
              <Box className="w-full text-left">
                <Textarea
                  label="Suggested Fix"
                  placeholder="If the reporter knows a direction"
                  {...register('suggestedFix')}
                  labelProps={{
                    className: 'font-semibold text-sm text-primary',
                  }}
                  minRows={4}
                  autosize
                />
              </Box>
            </div>

            <Box className="w-full text-left mb-[36px]">
              <Textarea
                label="Additional Context"
                placeholder="User Impact, Client feedback etc."
                {...register('additionalContext')}
                labelProps={{ className: 'font-semibold text-sm text-primary' }}
                minRows={4}
                autosize
              />
            </Box>

            <Box className="w-full flex justify-end">
              <Button
                type="submit"
                color="var(--color-primary)"
                leftSection={<Plus size={20} />}
                loading={createIncidentMutation.isPending}
                disabled={createIncidentMutation.isPending}
              >
                {createIncidentMutation.isPending
                  ? 'Creating...'
                  : 'Create Incident'}
              </Button>
            </Box>
          </form>
        </Stack>
      </Paper>
    </Container>
  );
};

export default CreateIncident;
