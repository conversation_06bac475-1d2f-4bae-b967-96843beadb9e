# Celery
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"
# Database
DATABASE_URL="sqlite:///./test.db"
POSTGRES_USER="ADD_YOUR_POSTGRES_USER"
POSTGRES_PASSWORD="ADD_YOUR_POSTGRES_PASSWORD"
POSTGRES_DB="ADD_YOUR_POSTGRES_DB_NAME"
# Gemini LLM
GEMINI_API_KEY="ADD_YOUR_KEY_HERE"
GOOGLE_GENAI_USE_VERTEXAI=false
# Github Connector via Access Token
GITHUB_ACCESS_TOKEN="ADD_YOUR_GITHUB_TOKEN"
# Github Connector via Github App
GITHUB_APP_ID="APP_ID"
GITHUB_APP_CLIENT_ID="APP_CLIENT_ID"
GITHUB_APP_CLIENT_SECRET="APP_CLIENT_SECRET"
GITHUB_APP_PRIVATE_KEY="PRIVATE_KEY"
# Logs Connector
LOKI_BASE_URL="http://loki:3100/loki"
# OpenAI LLM
OPENAI_API_KEY="ADD_YOUR_KEY_HERE"
# User Authentication
SECRET_KEY='ENTER_YOUR_SECRET_KEY_HERE'
ALGORITHM='HS256'
ACCESS_TOKEN_EXPIRE_MINUTES=30
# Vector DB
QDRANT_URL="http://qdrant:6333"
