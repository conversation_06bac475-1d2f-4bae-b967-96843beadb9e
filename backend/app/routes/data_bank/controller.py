from uuid import UUID

from database.core import DbSession
from entities.job import <PERSON>StatusEnum, JobTypeEnum
from fastapi import APIRouter, Body, HTTPException, Path, status
from utils.logger import get_service_logger

from routes.auth.service import CurrentUser
from routes.jobs import service as jobs_service

from . import models, service

# Initialize logger for the data bank controller
logger = get_service_logger("data_bank_controller")

# Create a new FastAPI router for the data_bank endpoints
router = APIRouter(prefix="/data_bank", tags=["Data Bank - [Testing]"])


@router.post(
    "/upsert-incident",
    status_code=status.HTTP_200_OK,
    response_model=dict,
)
def upsert_incident(
    db: DbSession,
    incident_id: UUID = Body(
        ..., description="The unique identifier of the incident to upsert"
    ),
):
    """
    Endpoint to create or update an incident's vector embedding.

    This endpoint receives an incident ID, retrieves the incident data from the database,
    and passes it to the service layer for embedding and storage in the vector database.
    The upsert operation handles both creation of new incidents and updating of existing ones.

    - **incident_id**: The unique identifier of the incident to be upserted.
    """
    try:
        logger.info(f"Received request to upsert incident: {incident_id}")
        success = service.upsert_incident(db, incident_id)
        if success:
            return {"status": "success", "message": "Incident upserted successfully."}
        else:
            # This case handles non-exception failures from the service layer
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upsert incident for an unknown reason.",
            )
    except ValueError as e:
        logger.warning(f"Bad request for upserting incident {incident_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {e}",
        )
    except Exception as e:
        logger.error(f"Error upserting incident {incident_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.delete(
    "/delete-incident/{incident_id}",
    status_code=status.HTTP_200_OK,
    response_model=dict,
)
def delete_incident(
    incident_id: UUID = Path(
        ..., description="The unique identifier of the incident to delete"
    ),
):
    """
    Endpoint to delete an incident's vector embedding.

    This endpoint receives an incident ID and removes the corresponding vector
    embedding from the vector database.

    - **incident_id**: The unique identifier of the incident to delete.
    """
    try:
        logger.info(f"Received request to delete incident: {incident_id}")
        success = service.delete_incident(incident_id)
        if success:
            return {"status": "success", "message": "Incident deleted successfully."}
        else:
            # This case handles non-exception failures from the service layer
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete incident for an unknown reason.",
            )
    except Exception as e:
        logger.error(f"Error deleting incident {incident_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.post(
    "/upsert-document",
    status_code=status.HTTP_200_OK,
    response_model=dict,
)
def upsert_document(
    db: DbSession,
    document_id: UUID = Body(
        ..., description="The unique identifier of the document to upsert"
    ),
):
    """
    Endpoint to create or update a document's vector embedding.

    This endpoint receives a document ID, retrieves the document data from the database,
    and passes it to the service layer for embedding and storage in the vector database.
    The upsert operation handles both creation of new documents and updating of existing ones.

    - **document_id**: The unique identifier of the document to be upserted.
    """
    try:
        logger.info(f"Received request to upsert document: {document_id}")
        success = service.upsert_document(db, document_id)
        if success:
            return {"status": "success", "message": "Document upserted successfully."}
        else:
            # This case handles non-exception failures from the service layer
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upsert document for an unknown reason.",
            )
    except ValueError as e:
        logger.warning(f"Bad request for upserting document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {e}",
        )
    except Exception as e:
        logger.error(f"Error upserting document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.delete(
    "/delete-document/{document_id}",
    status_code=status.HTTP_200_OK,
    response_model=dict,
)
def delete_document(
    document_id: UUID = Path(
        ..., description="The unique identifier of the document to delete"
    ),
):
    """
    Endpoint to delete a document's vector embedding.

    This endpoint receives a document ID and removes the corresponding vector
    embedding from the vector database.

    - **document_id**: The unique identifier of the document to delete.
    """
    try:
        logger.info(f"Received request to delete document: {document_id}")
        success = service.delete_document(document_id)
        if success:
            return {"status": "success", "message": "Document deleted successfully."}
        else:
            # This case handles non-exception failures from the service layer
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete document for an unknown reason.",
            )
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.post(
    "/similarity-search",
    response_model=models.CombinedSimilarityResponse,
)
def similarity_search(
    db: DbSession, search_request: models.TextSearchRequest = Body(...)
):
    """
    Endpoint to find similar content across both incidents and documents.

    This endpoint accepts any text input (title, description, summary, logs,
    error messages, etc.) and returns lists of the most semantically similar
    incidents and documents from both vector collections.

    - **search_request**: Contains the text to search and optional top_k parameter.

    Returns:
        CombinedSimilarityResponse: A dictionary containing:
        - similar_incidents: List of similar incidents with scores
        - relevant_documentations: List of relevant documents with scores
    """
    try:
        logger.info(
            f"Received combined similarity search request (text length={len(search_request.text)} chars)"
        )
        combined_results = service.search_similar_content(
            db=db, text=search_request.text, top_k=search_request.top_k
        )
        return combined_results
    except ValueError as e:
        logger.warning(f"Bad request for combined similarity search: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {e}",
        )
    except Exception as e:
        logger.error(f"Error during combined similarity search: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.post(
    "/trigger-embedding-job",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=dict,
)
def trigger_embedding_job(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID = Body(
        ...,
        description="The unique identifier of the incident to be embedded and upserted",
    ),
):
    """
    Trigger async incident embedding job via Celery.

    This endpoint queues an asynchronous embedding generation and upsert task
    for the provided incident ID. It returns immediately with a job ID that can
    be used to monitor the job status via the existing jobs API at `/jobs/status/{job_id}`.

    - **incident_id**: The unique identifier of the incident to be embedded and upserted.
    """
    try:
        from tasks.vector_db import upsert_incident_embedding_task

        logger.info(
            f"Received request to trigger async embedding job for: {incident_id}"
        )

        # Queue the async embedding task with just the incident ID
        task = upsert_incident_embedding_task.delay(str(incident_id))

        # Create a job record using the existing jobs service
        job_data = {
            "job_id": UUID(task.id),
            "job_type": JobTypeEnum.INCIDENT_EMBEDDING_UPSERT,
            "status": JobStatusEnum.PENDING,
        }
        job = jobs_service.create_job(db, current_user, job_data)

        logger.info(f"Successfully created embedding job with job_id: {job.job_id}")

        return {
            "status": "accepted",
            "message": "Incident embedding job queued successfully",
            "job_id": str(job.job_id),
            "incident_id": str(incident_id),
            "status_endpoint": f"/jobs/status/{job.job_id}",
        }
    except Exception as e:
        logger.error(f"Failed to queue embedding job for incident {incident_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue embedding job: {e}",
        )


@router.post(
    "/trigger-document-embedding-job",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=dict,
)
def trigger_document_embedding_job(
    db: DbSession,
    current_user: CurrentUser,
    document_id: UUID = Body(
        ...,
        description="The unique identifier of the document to be embedded and upserted",
    ),
):
    """
    Trigger async document embedding job via Celery.

    This endpoint queues an asynchronous embedding generation and upsert task
    for the provided document ID. It returns immediately with a job ID that can
    be used to monitor the job status via the existing jobs API at `/jobs/status/{job_id}`.

    - **document_id**: The unique identifier of the document to be embedded and upserted.
    """
    try:
        from tasks.vector_db import upsert_document_embedding_task

        logger.info(
            f"Received request to trigger async document embedding job for: {document_id}"
        )

        # Queue the async embedding task with just the document ID
        task = upsert_document_embedding_task.delay(str(document_id))

        # Create a job record using the existing jobs service
        job_data = {
            "job_id": UUID(task.id),
            "job_type": JobTypeEnum.DOCUMENT_EMBEDDING_UPSERT,
            "status": JobStatusEnum.PENDING,
        }
        job = jobs_service.create_job(db, current_user, job_data)

        logger.info(
            f"Successfully created document embedding job with job_id: {job.job_id}"
        )

        return {
            "status": "accepted",
            "message": "Document embedding job queued successfully",
            "job_id": str(job.job_id),
            "document_id": str(document_id),
            "status_endpoint": f"/jobs/status/{job.job_id}",
        }
    except Exception as e:
        logger.error(
            f"Failed to queue document embedding job for document {document_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to queue document embedding job: {e}",
        )
