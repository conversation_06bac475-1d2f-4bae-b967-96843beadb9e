from typing import List
from uuid import UUID

from database.core import DbSession
from db_services import incident as incident_db_service
from db_services import runbooks as runbooks_db_service
from fastapi import APIRouter, HTTPException, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser

from . import models, service

logger = get_controller_logger("runbooks")
router = APIRouter(prefix="/incidents/{incident_id}/runbooks", tags=["Runbooks"])


@router.post(
    "", response_model=models.RunbookResponse, status_code=status.HTTP_201_CREATED
)
async def create_runbook(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    payload: models.RunbookCreate,
):
    logger.info(
        f"Creating runbook for incident {incident_id} by user {current_user.get_uuid()}"
    )
    try:
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Incident not found"
            )

        result = service.create_runbook(db, incident_id, payload)
        logger.info(f"Successfully created runbook with ID: {result.id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Runbook creation failed for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Runbook creation failed: {str(e)}",
        )


@router.get("", response_model=list[models.RunbookResponse])
async def list_runbooks(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
):
    logger.info(f"Listing runbooks for incident: {incident_id}")
    try:
        result = service.list_runbooks(db, incident_id)
        logger.info(f"Retrieved {len(result)} runbooks for incident {incident_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to list runbooks for incident {incident_id}: {str(e)}")
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list runbooks: {str(e)}",
        )


@router.put("/{runbook_id}", response_model=models.RunbookResponse)
async def update_runbook(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: UUID,
    payload: models.RunbookUpdate,
):
    logger.info(f"Updating runbook {runbook_id} for incident {incident_id}")
    try:
        result = service.update_runbook(db, incident_id, runbook_id, payload)
        logger.info(f"Successfully updated runbook: {runbook_id}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update runbook {runbook_id}: {str(e)}")
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update runbook: {str(e)}",
        )


@router.delete("/{runbook_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_runbook(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: UUID,
):
    logger.info(f"Deleting runbook {runbook_id} for incident {incident_id}")
    try:
        service.delete_runbook(db, incident_id, runbook_id)
        logger.info(f"Successfully deleted runbook: {runbook_id}")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete runbook {runbook_id}: {str(e)}")
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete runbook: {str(e)}",
        )


@router.get(
    "/{runbook_id}/steps",
    response_model=List[models.RunbookStepResponse],
)
async def get_steps(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: UUID,
):
    logger.info(f"Getting steps for runbook {runbook_id} in incident {incident_id}")
    try:
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Incident with ID {incident_id} not found",
            )
        runbook = runbooks_db_service.get_runbook_by_id(db, runbook_id)
        if not runbook:
            logger.warning(f"Runbook not found: {runbook_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Runbook with ID {runbook_id} not found",
            )
        steps = runbooks_db_service.get_steps_by_runbook(db, runbook_id)
        if not steps:
            logger.info(
                f"No existing steps found for runbook {runbook_id}, generating new steps"
            )
            steps = await service.generate_steps(db, current_user, incident, runbook)

        logger.info(f"Retrieved {len(steps)} steps for runbook {runbook_id}")
        return steps
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to fetch steps for runbook {runbook_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch steps: {str(e)}",
        )


@router.post(
    "/{runbook_id}/generate_steps", response_model=List[models.RunbookStepResponse]
)
async def generate_steps(
    db: DbSession,
    current_user: CurrentUser,
    runbook_id: UUID,
    incident_id: UUID,
):
    logger.info(f"Generating steps for runbook {runbook_id} in incident {incident_id}")
    try:
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Incident with ID {incident_id} not found",
            )
        runbook = runbooks_db_service.get_runbook_by_id(db, runbook_id)
        if not runbook:
            logger.warning(f"Runbook not found: {runbook_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Runbook with ID {runbook_id} not found",
            )
        steps = await service.generate_steps(db, current_user, incident, runbook)
        logger.info(f"Generated {len(steps)} steps for runbook {runbook_id}")
        return [models.RunbookStepResponse.model_validate(step) for step in steps]

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to generate steps for runbook {runbook_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate steps: {e}",
        )


@router.put("/{runbook_id}/steps/{step_id}", response_model=models.RunbookStepResponse)
async def update_step(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: UUID,
    step_id: UUID,
    payload: models.RunbookStepUpdate,
):
    """Update a runbook step's status and notes"""
    logger.info(
        f"Updating step {step_id} for runbook {runbook_id} in incident {incident_id}"
    )
    try:
        # Verify incident exists
        incident = incident_db_service.get_incident_by_id(db, incident_id)
        if not incident:
            logger.warning(f"Incident not found: {incident_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Incident with ID {incident_id} not found",
            )

        # Update the step
        updated_step = service.update_runbook_step(
            db, incident_id, runbook_id, step_id, current_user, payload
        )
        logger.info(f"Successfully updated step: {step_id}")
        return models.RunbookStepResponse.model_validate(updated_step)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update step {step_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update step: {str(e)}",
        )


@router.delete("/{runbook_id}/steps/{step_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_runbook_step(
    db: DbSession,
    current_user: CurrentUser,
    incident_id: UUID,
    runbook_id: UUID,
    step_id: UUID,
):
    logger.info(
        f"Deleting step {step_id} for runbook {runbook_id} in incident {incident_id}"
    )
    try:
        service.delete_runbook_step(db, incident_id, runbook_id, step_id)
        logger.info(f"Successfully deleted step: {step_id}")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete step {step_id}: {str(e)}")
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete runbook step: {str(e)}",
        )
