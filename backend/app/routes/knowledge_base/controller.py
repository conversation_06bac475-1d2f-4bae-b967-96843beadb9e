from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, status
from utils.logger import get_controller_logger

from routes.auth.service import CurrentUser
from routes.knowledge_base import models, service

logger = get_controller_logger("knowledge-base")
router = APIRouter(prefix="/knowledge-base", tags=["KnowledgeBase"])


@router.post(
    "/create",
    response_model=models.KnowledgeBaseResponse,
    status_code=status.HTTP_201_CREATED,
)
def create_entry(
    data: models.KnowledgeBaseCreate, db: DbSession, current_user: CurrentUser
):
    logger.info(
        f"Creating knowledge base entry for user {current_user.get_uuid()}: {data.title}"
    )
    try:
        result = service.create_kb_entry(db, data, current_user.get_uuid())
        logger.info(f"Successfully created knowledge base entry with ID: {result.id}")
        return result
    except Exception as e:
        logger.error(f"Failed to create knowledge base entry: {str(e)}")
        raise


@router.get("/", response_model=models.PaginatedKnowledgeBaseResponse)
def get_entries(
    db: DbSession, current_user: CurrentUser, offset: int = 0, limit: int = 10
):
    logger.info(f"Getting knowledge base entries with offset={offset}, limit={limit}")
    try:
        entries, total = service.get_kb_entries(db, offset, limit)
        pages = (total + limit - 1) // limit
        page = (offset // limit) + 1

        logger.info(
            f"Retrieved {len(entries)} knowledge base entries out of {total} total"
        )
        return {
            "items": entries,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
    except Exception as e:
        logger.error(f"Failed to get knowledge base entries: {str(e)}")
        raise


@router.get("/{entry_id}", response_model=models.KnowledgeBaseResponse)
def get_entry(entry_id: UUID, db: DbSession, current_user: CurrentUser):
    logger.info(f"Getting knowledge base entry: {entry_id}")
    try:
        result = service.get_kb_entry_by_id(db, entry_id)
        logger.info(f"Successfully retrieved knowledge base entry: {entry_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to get knowledge base entry {entry_id}: {str(e)}")
        raise


@router.put("/{entry_id}", response_model=models.KnowledgeBaseResponse)
def update_entry(
    entry_id: UUID,
    data: models.KnowledgeBaseUpdate,
    db: DbSession,
    current_user: CurrentUser,
):
    logger.info(f"Updating knowledge base entry: {entry_id}")
    try:
        result = service.update_kb_entry(db, entry_id, data)
        logger.info(f"Successfully updated knowledge base entry: {entry_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to update knowledge base entry {entry_id}: {str(e)}")
        raise


@router.delete("/{entry_id}", status_code=status.HTTP_200_OK)
def delete_entry(entry_id: UUID, db: DbSession, current_user: CurrentUser):
    logger.info(f"Deleting knowledge base entry: {entry_id}")
    try:
        result = service.delete_kb_entry(db, entry_id)
        logger.info(f"Successfully deleted knowledge base entry: {entry_id}")
        return result
    except Exception as e:
        logger.error(f"Failed to delete knowledge base entry {entry_id}: {str(e)}")
        raise
