from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class KnowledgeBaseCreate(BaseModel):
    system_architecture: str = Field(
        ..., description="High-level architecture overview"
    )
    aims_and_scopes: str = Field(..., description="Goals and scope of the project")
    project_description: str = Field(..., description="Detailed project description")


class KnowledgeBaseUpdate(BaseModel):
    system_architecture: Optional[str] = None
    aims_and_scopes: Optional[str] = None
    project_description: Optional[str] = None


class KnowledgeBaseResponse(KnowledgeBaseCreate):
    id: UUID
    updated_by: UUID
    updated_at: datetime

    class Config:
        from_attributes = True


class PaginatedKnowledgeBaseResponse(BaseModel):
    items: List[KnowledgeBaseResponse]
    total: int
    page: int
    limit: int
    pages: int
