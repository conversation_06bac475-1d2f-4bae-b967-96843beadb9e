from datetime import datetime, timezone
from typing import List, Tuple
from uuid import UUID

from database.core import DbSession
from db_services import knowledge_base as knowledge_base_db_service
from entities.knowledge_base import KnowledgeBase
from fastapi import HTTPException, status
from utils.logger import get_service_logger

from routes.knowledge_base.models import KnowledgeBaseCreate, KnowledgeBaseUpdate

logger = get_service_logger("knowledge_base")


def create_kb_entry(
    db: DbSession, data: KnowledgeBaseCreate, user_id: UUID
) -> KnowledgeBase:
    logger.info(f"Creating knowledge base entry for user {user_id}")
    try:
        new_entry = KnowledgeBase(
            system_architecture=data.system_architecture,
            aims_and_scopes=data.aims_and_scopes,
            project_description=data.project_description,
            updated_by=user_id,
            updated_at=datetime.now(timezone.utc),
        )
        db.add(new_entry)
        db.commit()
        db.refresh(new_entry)
        logger.info(
            f"Successfully created knowledge base entry with ID: {new_entry.id}"
        )
        return new_entry
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create knowledge base entry: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create KB entry",
        )


def update_kb_entry(
    db: DbSession, entry_id: UUID, data: KnowledgeBaseUpdate
) -> KnowledgeBase:
    logger.info(f"Updating knowledge base entry: {entry_id}")
    entry = knowledge_base_db_service.get_kb_entry_by_id(db, entry_id)
    if not entry:
        logger.warning(f"Knowledge base entry not found: {entry_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="KB entry not found"
        )
    update_data = data.model_dump(exclude_unset=True)

    for key, value in update_data.items():
        setattr(entry, key, value)

    try:
        db.commit()
        db.refresh(entry)
        logger.info(f"Successfully updated knowledge base entry: {entry_id}")
        return entry
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to update knowledge base entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update KB entry",
        )


def delete_kb_entry(db: DbSession, entry_id: UUID) -> dict:
    logger.info(f"Deleting knowledge base entry: {entry_id}")
    entry = knowledge_base_db_service.get_kb_entry_by_id(db, entry_id)
    if not entry:
        logger.warning(f"Knowledge base entry not found for deletion: {entry_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="KB entry not found"
        )

    try:
        db.delete(entry)
        db.commit()
        logger.info(f"Successfully deleted knowledge base entry: {entry_id}")
        return {"message": f"Entry {entry_id} deleted"}
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete knowledge base entry {entry_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete KB entry",
        )


def get_kb_entries(
    db: DbSession, offset: int = 0, limit: int = 10
) -> Tuple[List[KnowledgeBase], int]:
    logger.info(f"Getting knowledge base entries with offset={offset}, limit={limit}")
    try:
        entries, total = knowledge_base_db_service.get_kb_entries(db, offset, limit)
        logger.info(
            f"Retrieved {len(entries)} knowledge base entries out of {total} total"
        )
        return entries, total
    except Exception as e:
        logger.error(f"Failed to get knowledge base entries: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get KB entries",
        )
