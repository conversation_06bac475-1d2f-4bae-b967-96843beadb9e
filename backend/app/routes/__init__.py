from fastapi import FastAPI

from routes.agents.controller import router as agents_router
from routes.auth.controller import router as auth_router
from routes.dashboard.controller import router as dashboard_router
from routes.data_bank.controller import router as data_bank_router
from routes.events.controller import router as events_router
from routes.incident_metrics.controller import router as incident_metrics_router
from routes.incident_report.controller import router as report_router
from routes.incidents.controller import router as incidents_router
from routes.jobs.controller import router as jobs_router
from routes.knowledge_base.controller import router as knowledge_base_router
from routes.logs.controller import router as logs_router
from routes.runbooks.controller import router as runbooks_router
from routes.users.controller import router as users_router


def register_routes(app: FastAPI):
    app.include_router(auth_router)
    app.include_router(agents_router)
    app.include_router(incidents_router)
    app.include_router(jobs_router)
    app.include_router(logs_router)
    app.include_router(users_router)
    app.include_router(runbooks_router)
    app.include_router(knowledge_base_router)
    app.include_router(events_router)
    app.include_router(incident_metrics_router)
    app.include_router(dashboard_router)
    app.include_router(report_router)
    app.include_router(data_bank_router)
