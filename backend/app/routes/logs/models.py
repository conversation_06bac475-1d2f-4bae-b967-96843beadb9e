from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, model_validator
from typing_extensions import TypedDict


class PaginatedLogsResult(TypedDict):
    data: List["LogEntry"]
    next_cursor: Optional[str]
    has_more: bool


class LogQueryParams(BaseModel):
    query: str = Field(
        ...,
        examples=[
            '{job=~".+"}',
            '{job="system-logs"}',
            '{job="system-logs"} |= "Failed password"',
        ],
    )
    start: str = Field(
        ...,
        examples=[
            (datetime.now(timezone.utc) - timedelta(days=1)).strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            )
        ],
    )
    end: str = Field(
        ..., examples=[datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")]
    )
    limit: int = Field(100, le=1000)  # Max 1000 logs per request
    direction: Literal["backward", "forward"] = Field("backward")
    cursor: Optional[str] = Field(
        None, description="Cursor for pagination (timestamp in nanoseconds)"
    )

    @model_validator(mode="after")
    def check_required_fields(self):
        if not self.query or not self.start or not self.end:
            raise ValueError(
                "Missing one or more required parameters: query, start, or end"
            )
        return self


class LogEntry(BaseModel):
    timestamp: int
    line: str
    labels: Dict[str, Any]


class LogsResponse(BaseModel):
    status_code: int
    message: str
    data: List[LogEntry]
    next_cursor: Optional[str] = None
    has_more: bool = False
