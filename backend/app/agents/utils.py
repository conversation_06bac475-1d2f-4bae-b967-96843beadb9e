import uuid
from collections.abc import As<PERSON><PERSON>enerator
from datetime import datetime, timezone
from typing import Optional

from fastapi import HTTPEx<PERSON>
from google.adk.agents import LlmAgent
from utils.logger import get_service_logger

from agents.agent_runner import (
    call_agent_async,
    create_session,
    get_runner,
    get_session,
)

# Setup centralized logging
logger = get_service_logger("agents_utils")


async def get_final_response(event_generator: AsyncGenerator) -> str:
    final_response_text = "Agent did not produce a final response."  # Default
    async for event in event_generator:
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
            elif event.actions and event.actions.escalate:
                final_response_text = (
                    f"Agent escalated: {event.error_message or 'No specific message.'}"
                )
            break  # Stop processing events once the final response is found
    return final_response_text


def generate_session_id(user_id: str) -> str:
    """
    Generate a unique session ID using a UUID based on the current datetime.
    """

    dt_str = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")
    return f"session_{dt_str}_{user_id}_{uuid.uuid4().hex}"


async def handle_agent_request(
    user_id: str, session_id: Optional[str], query: str, agent: LlmAgent
) -> AsyncGenerator:
    APP_NAME = "test_app"
    initial_state = {"user:preferences": {"language": "English"}, "user:messages": []}
    if not user_id:
        logger.warning("Unauthorized agent request attempt")
        raise HTTPException(status_code=401, detail="Unauthorized")
    logger.info(f"Processing agent request for user {user_id}")
    if not session_id or await get_session(APP_NAME, str(user_id), session_id) is None:
        session_id = generate_session_id(user_id)
        await create_session(APP_NAME, str(user_id), session_id, initial_state)
    runner = get_runner(APP_NAME, agent)
    event_generator = call_agent_async(query, runner, str(user_id), session_id)
    logger.info(f"Agent request processed for user {user_id}")
    return event_generator


def format_agent_event_for_sse(event) -> dict:
    """
    Format a Google ADK agent event for Server-Sent Events (SSE) streaming.

    Args:
        event: Google ADK event object

    Returns:
        dict: Formatted event data suitable for JSON serialization
    """
    event_data = {
        "author": event.author,
        "type": type(event).__name__,
        "is_final": event.is_final_response(),
        "content": None,
        "error_message": getattr(event, "error_message", None),
        "timestamp": None,
    }

    # Extract content if available
    if event.content and event.content.parts:
        event_data["content"] = event.content.parts[0].text
    elif hasattr(event, "actions") and event.actions and event.actions.escalate:
        event_data["content"] = (
            f"Agent escalated: {event.error_message or 'No specific message.'}"
        )

    # Add timestamp if available
    if hasattr(event, "timestamp"):
        event_data["timestamp"] = str(event.timestamp)

    return event_data
