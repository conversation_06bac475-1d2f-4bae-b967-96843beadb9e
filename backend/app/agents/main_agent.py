from google.adk.agents import Agent
from google.adk.models.lite_llm import Lite<PERSON><PERSON>
from google.adk.tools.agent_tool import AgentTool

from agents.guardrail import block_keyword_guardrail
from agents.sub_agents import (
    incident_manager_agent,
    log_analytics_agent,
    preference_agent,
    report_agent,
    root_cause_analyzer,
    runbook_generator_agent,
    time_agent,
)

from . import prompt

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"

coordinator_agent = Agent(
    name="sre_incident_assistant",
    model=LiteLlm(AGENT_MODEL),
    description="SRE Incident Assistant that helps resolve incidents through systematic investigation and iterative problem-solving.",
    instruction=prompt.INSTRUCTION,
    sub_agents=[time_agent, preference_agent],
    tools=[
        AgentTool(agent=incident_manager_agent, skip_summarization=False),
        Agent<PERSON><PERSON>(agent=log_analytics_agent, skip_summarization=False),
        <PERSON><PERSON><PERSON>(agent=root_cause_analyzer, skip_summarization=False),
        Agent<PERSON><PERSON>(agent=runbook_generator_agent, skip_summarization=False),
        Agent<PERSON><PERSON>(agent=report_agent, skip_summarization=False),
    ],
    before_model_callback=block_keyword_guardrail,
)
