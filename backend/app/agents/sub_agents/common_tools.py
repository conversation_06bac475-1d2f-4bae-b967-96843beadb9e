from datetime import datetime


def get_current_datetime():
    """Gets the current date and time.
    Use this tool whenever you need the current date and time.

    Returns:
        str: The current date and time in the format 'YYYY-MM-DD HH:MM:SS'.
    """

    return f"Current date with time is {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"


def get_configured_services():
    """Retrieves the list of configured services in the system.

    Use this tool to get a list of all services that are currently configured
    in the system architecture. This is useful for understanding the context
    of the incident and identifying affected components.

    Returns:
        list: A list of service names that are configured in the system.
    """
    return [
        {
            "service_name": "gateway_service",
            "description": "Handles incoming requests and routes them to appropriate services.",
            "additional_info": "Uses Nginx for load balancing and routing.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": [],
                "downstream_services": ["backend_service", "frontend"],
            },
        },
        {
            "service_name": "backend_service",
            "description": "Processes business logic and interacts with the database.",
            "additional_info": "Built with Fastapi, Pydantic and SQLAlchemy v2.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": ["gateway_service"],
                "downstream_services": [
                    "database_service",
                    "payment_service",
                    "celery_worker",
                ],
                "failover_services": [],
            },
        },
        {
            "service_name": "database_service",
            "description": "Stores application data and handles queries.",
            "additional_info": "PostgreSQL database with replication enabled.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": ["backend_service"],
                "downstream_services": [],
                "failover_services": ["Database Replica available"],
            },
        },
        {
            "service_name": "payment_service",
            "description": "Handles payment processing and transactions.",
            "additional_info": "Integrates with Stripe and PayPal APIs for payment processing.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": ["backend_service"],
                "downstream_services": [],
            },
            "failover_services": [],
        },
        {
            "service_name": "frontend",
            "description": "User interface for the application, interacts with backend services.",
            "additional_info": "Built with React, Vite, TypeScript, Tanstack Query, and Tailwind CSS.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": ["gateway_service"],
                "downstream_services": [],
            },
            "failover_services": [],
        },
        {
            "service_name": "celery_worker",
            "description": "Handles asynchronous task processing and background jobs.",
            "additional_info": "Uses Celery with Redis for task queuing.",
            "ip_address": "***********",
            "dependencies": {
                "upstream_services": ["backend_service"],
                "downstream_services": [],
                "failover_services": ["multiple instances of celery_workers available"],
            },
        },
    ]
