from typing import List

from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field

from . import prompt


class step_schema(BaseModel):
    name: str = Field(..., description="Concise title of the action to be performed")
    purpose: str = Field(..., description="Why this action is needed")
    details: str = Field(
        ...,
        description="Technical steps to execute, including any commands, code, warnings, or context",
    )
    expectedOutcome: str = Field(
        ..., description="What should happen if this step succeeds"
    )


class output_schema(BaseModel):
    steps: List[step_schema]


AGENT_MODEL = "gemini/gemini-2.0-flash"
runbook_generator_agent = LlmAgent(
    name="runbook_generator_agent",
    model=LiteLlm(AGENT_MODEL),
    description="Analyzes technical information about incidents such as description, attachments, root cause and provide actionable solutions in the form of runbooks",
    instruction=prompt.INSTRUCTION,
    output_schema=output_schema,
    output_key="runbook",
    tools=[],
)
