import os
from contextlib import contextmanager
from typing import List, Optional
from uuid import UUID

from database.core import get_db
from db_services import incident as incident_db_service
from pydantic import BaseModel, Field


class SimilarIncident(BaseModel):
    incident_id: str = Field(..., description="Similar incident ID")
    title: str = Field(..., description="Similar incident title")
    similarity_score: float = Field(..., description="Similarity score (0.0-1.0)")
    resolution_summary: str = Field(..., description="How it was resolved")
    root_cause: str = Field(..., description="Root cause of the similar incident")
    affected_services: List[str] = Field(
        ..., description="Services affected in similar incident"
    )


class DocumentationItem(BaseModel):
    title: str = Field(..., description="Documentation title")
    url: str = Field(..., description="Documentation URL or path")
    relevance_score: float = Field(..., description="Relevance score (0.0-1.0)")
    summary: str = Field(..., description="Brief summary of the documentation")
    type: str = Field(
        ...,
        description="Type of documentation (runbook, troubleshooting, architecture)",
    )


@contextmanager
def get_db_session():
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


def get_recent_incidents(limit: int) -> List[dict]:
    """Retrieves a list of recent incidents with optional filtering by status.

    Args:
        limit: Maximum number of incidents to retrieve, defaults to 10.

    Returns:
        List[dict]: A list of dictionaries containing incident details.

    Raises:
        Exception: For database or other unexpected errors.
    """
    try:
        print(f"Getting recent incidents from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            incidents = incident_db_service.get_recent_incidents(db, limit)
            return [
                {
                    "incident_id": str(incident.id),
                    "incident_number": incident.incident_number,
                    "title": incident.title,
                    "summary": incident.summary,
                    "status": incident.status.value if incident.status else None,
                    "reported_at": (
                        incident.reported_at.isoformat()
                        if incident.reported_at
                        else None
                    ),
                    "updated_at": (
                        incident.updated_at.isoformat() if incident.updated_at else None
                    ),
                }
                for incident in incidents
            ]
    except Exception as e:
        print(f"Unexpected error retrieving recent incidents: {str(e)}")
        raise Exception(f"Failed to retrieve recent incidents: {str(e)}") from e


def get_incident_details(
    incident_id: Optional[str],
    incident_number: Optional[str],
) -> dict:
    """Retrieves the details of a specific incident by incident_id or incident_number.

    Args:
        incident_id: The UUID of the incident to retrieve. If incident_id is not provided, incident_number must be provided.
        incident_number: The incident number of the incident to retrieve. If incident_number is not provided, incident_id must be provided.

    Returns:
        dict: A dictionary containing the incident details.

    Raises:
        ValueError: If neither incident_id nor incident_number is provided,
                   or if the incident is not found.
        Exception: For database or other unexpected errors.
    """
    if not incident_id and not incident_number:
        raise ValueError("Must specify either incident_id or incident_number")

    try:
        print(f"Getting incident details from {os.getenv('DATABASE_URL')}")
        with get_db_session() as db:
            if incident_number:
                incident = incident_db_service.get_incident_by_number(
                    db, incident_number
                )
            elif incident_id:
                try:
                    incident_uuid = UUID(incident_id)
                except ValueError as e:
                    raise ValueError(
                        f"Invalid incident_id format: {incident_id}"
                    ) from e

                incident = incident_db_service.get_incident_by_id(db, incident_uuid)
            if not incident:
                raise ValueError(f"Incident not found with id: {incident_id}")

            result = {
                "incident_id": str(incident.id),
                "incident_number": incident.incident_number,
                "title": incident.title,
                "summary": incident.summary,
                "priority": incident.priority.value if incident.priority else None,
                "severity": incident.severity.value if incident.severity else None,
                "incident_type": (
                    incident.incident_type.value if incident.incident_type else None
                ),
                "status": incident.status.value if incident.status else None,
                "reported_at": (
                    incident.reported_at.isoformat() if incident.reported_at else None
                ),
                "updated_at": (
                    incident.updated_at.isoformat() if incident.updated_at else None
                ),
            }

            return result

    except ValueError:
        raise
    except Exception as e:
        print(f"Unexpected error retrieving incident details: {str(e)}")
        raise Exception(f"Failed to retrieve incident details: {str(e)}") from e


def find_similar_incidents(
    text: str,
    limit: int,
) -> List[SimilarIncident]:
    """
    Find similar past incidents based on affected services, description, and tags.

    Args:
        text: Description of current incident
        limit: Maximum number of similar incidents to return, defaults to 5

    Returns:
        List of similar incidents with resolution information
    """
    # TODO: Implement actual similarity search using vector databases and text embeddings

    historical_incidents = [
        {
            "incident_id": "INC-2023-0456",
            "title": "Database connection timeout issues",
            "similarity_score": 0.85,
            "resolution_summary": "Increased connection pool size from 50 to 100 and optimized slow queries",
            "root_cause": "Database connection pool exhaustion due to slow queries holding connections",
            "affected_services": ["database service", "backend service"],
        },
        {
            "incident_id": "INC-2023-0789",
            "title": "API response time degradation",
            "similarity_score": 0.78,
            "resolution_summary": "Added database indexes on frequently queried columns and implemented query caching",
            "root_cause": "Missing database indexes causing full table scans on large tables",
            "affected_services": ["backend service", "database service"],
        },
        {
            "incident_id": "INC-2023-0321",
            "title": "Redis cache performance issues",
            "similarity_score": 0.72,
            "resolution_summary": "Increased Redis memory allocation and optimized cache key patterns",
            "root_cause": "Redis memory pressure causing frequent cache evictions",
            "affected_services": ["backend service"],
        },
        {
            "incident_id": "INC-2023-0654",
            "title": "Database deadlock causing timeouts",
            "similarity_score": 0.68,
            "resolution_summary": "Optimized transaction ordering and reduced transaction duration",
            "root_cause": "Database deadlocks due to concurrent transactions accessing same resources",
            "affected_services": [
                "database service",
                "backend service",
                "payment service",
            ],
        },
    ]

    relevant_incidents = []
    for incident in historical_incidents:
        relevant_incidents.append(
            SimilarIncident(
                incident_id=incident["incident_id"],
                title=incident["title"],
                similarity_score=incident["similarity_score"],
                resolution_summary=incident["resolution_summary"],
                root_cause=incident["root_cause"],
                affected_services=incident["affected_services"],
            )
        )
    # Sort by similarity score and return top results
    relevant_incidents.sort(key=lambda x: x.similarity_score, reverse=True)
    return relevant_incidents[:limit]


def find_relevant_documentation(
    affected_services: List[str],
    incident_tags: Optional[List[str]],
    incident_description: str,
) -> List[DocumentationItem]:
    """
    Find relevant documentation based on affected services and incident characteristics.

    Args:
        affected_services: List of services affected in the incident
        incident_tags: Optional tags associated with the incident
        incident_description: Description of the incident

    Returns:
        List of relevant documentation items
    """
    if incident_tags is None:
        incident_tags = []

    # Mock documentation database - in real implementation, this would query a documentation system
    documentation_items = [
        {
            "title": "API Service Troubleshooting Guide",
            "url": "/docs/api-service/troubleshooting",
            "relevance_score": 0.9,
            "summary": "Comprehensive guide for diagnosing API service performance issues, including database connectivity problems and timeout resolution",
            "type": "troubleshooting",
        },
        {
            "title": "Database Connection Pool Configuration",
            "url": "/docs/database/connection-pool",
            "relevance_score": 0.85,
            "summary": "How to configure and tune database connection pools for optimal performance and avoid connection exhaustion",
            "type": "configuration",
        },
        {
            "title": "Performance Monitoring and Investigation Runbook",
            "url": "/runbooks/performance-investigation",
            "relevance_score": 0.8,
            "summary": "Step-by-step procedures for investigating performance degradation issues across all services",
            "type": "runbook",
        },
        {
            "title": "Redis Cache Architecture and Troubleshooting",
            "url": "/docs/cache/redis-troubleshooting",
            "relevance_score": 0.75,
            "summary": "Guide for diagnosing and resolving Redis cache issues, including memory management and failover procedures",
            "type": "troubleshooting",
        },
        {
            "title": "Database Query Optimization Guide",
            "url": "/docs/database/query-optimization",
            "relevance_score": 0.7,
            "summary": "Best practices for optimizing database queries, identifying slow queries, and improving database performance",
            "type": "guide",
        },
        {
            "title": "System Architecture and Service Dependencies",
            "url": "/docs/architecture/system-overview",
            "relevance_score": 0.65,
            "summary": "High-level overview of system architecture, service dependencies, and data flow patterns",
            "type": "architecture",
        },
        {
            "title": "Incident Response Procedures",
            "url": "/runbooks/incident-response",
            "relevance_score": 0.6,
            "summary": "Standard operating procedures for incident response, escalation, and communication",
            "type": "runbook",
        },
    ]

    # Filter and score based on relevance to affected services and tags
    relevant_docs = []
    for doc in documentation_items:
        relevance = doc["relevance_score"]

        # Boost relevance for service-specific documentation
        for service in affected_services:
            service_name = service.replace("-", " ").replace("_", " ")
            if (
                service_name in doc["title"].lower()
                or service_name in doc["summary"].lower()
            ):
                relevance += 0.15

        # Boost relevance for tag-specific documentation
        for tag in incident_tags:
            if (
                tag.lower() in doc["title"].lower()
                or tag.lower() in doc["summary"].lower()
            ):
                relevance += 0.1

        # Boost relevance for description keywords
        description_keywords = [
            "performance",
            "timeout",
            "connection",
            "database",
            "api",
            "cache",
        ]
        for keyword in description_keywords:
            if (
                keyword in incident_description.lower()
                and keyword in doc["summary"].lower()
            ):
                relevance += 0.05

        doc["relevance_score"] = min(relevance, 1.0)  # Cap at 1.0
        relevant_docs.append(DocumentationItem(**doc))

    # Sort by relevance and return
    relevant_docs.sort(key=lambda x: x.relevance_score, reverse=True)
    return relevant_docs
