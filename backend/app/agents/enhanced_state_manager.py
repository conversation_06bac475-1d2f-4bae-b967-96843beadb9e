"""
Enhanced State Management for Incident Resolution Workflows.

This module provides utilities for managing state across the multi-agent
incident resolution system, ensuring proper data flow and context preservation.
"""

from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class IncidentStatus(Enum):
    """Enumeration of possible incident statuses."""

    DETECTED = "detected"
    TRIAGED = "triaged"
    INVESTIGATING = "investigating"
    RESOLVING = "resolving"
    RESOLVED = "resolved"
    ESCALATED = "escalated"


class ResolutionPhase(Enum):
    """Enumeration of resolution workflow phases."""

    INITIAL_ANALYSIS = "initial_analysis"
    DATA_GATHERING = "data_gathering"
    RESOLUTION_LOOP = "resolution_loop"
    COMPLETED = "completed"


@dataclass
class IncidentContext:
    """Structured incident context for state management."""

    incident_id: str
    title: str
    description: str
    status: IncidentStatus
    priority: str
    created_at: datetime
    reporter: str
    assignee: Optional[str] = None
    affected_services: List[str]
    impact_assessment: Dict[str, Any]
    technical_indicators: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for state storage."""
        result = asdict(self)
        result["status"] = self.status.value
        result["created_at"] = self.created_at.isoformat()
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "IncidentContext":
        """Create from dictionary stored in state."""
        data["status"] = IncidentStatus(data["status"])
        data["created_at"] = datetime.fromisoformat(data["created_at"])
        return cls(**data)


@dataclass
class WorkflowState:
    """Tracks the overall workflow state and progress."""

    current_phase: ResolutionPhase
    phase_start_time: datetime
    iteration_count: int = 0
    completed_phases: List[ResolutionPhase] = []
    phase_results: Dict[str, Any] = {}

    def __post_init__(self):
        if self.completed_phases is None:
            self.completed_phases = []
        if self.phase_results is None:
            self.phase_results = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for state storage."""
        result = asdict(self)
        result["current_phase"] = self.current_phase.value
        result["phase_start_time"] = self.phase_start_time.isoformat()
        result["completed_phases"] = [phase.value for phase in self.completed_phases]
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "WorkflowState":
        """Create from dictionary stored in state."""
        data["current_phase"] = ResolutionPhase(data["current_phase"])
        data["phase_start_time"] = datetime.fromisoformat(data["phase_start_time"])
        data["completed_phases"] = [
            ResolutionPhase(phase) for phase in data["completed_phases"]
        ]
        return cls(**data)


class EnhancedStateManager:
    """
    Enhanced state manager for incident resolution workflows.

    Provides utilities for managing structured state data across
    the multi-agent incident resolution system.
    """

    # State key constants
    INCIDENT_CONTEXT_KEY = "incident_context"
    WORKFLOW_STATE_KEY = "workflow_state"
    EVIDENCE_DATA_KEY = "evidence_data"
    RESOLUTION_STATUS_KEY = "resolution_status"

    # Phase-specific state keys
    INITIAL_ANALYSIS_KEYS = {
        "incident_details": "incident_details",
        "affected_services": "affected_services",
        "timeline": "timeline",
    }

    DATA_GATHERING_KEYS = {
        "incident_logs": "incident_logs",
        "historical_context": "historical_context",
        "documentation": "documentation",
        "current_status": "current_status",
    }

    RESOLUTION_LOOP_KEYS = {
        "root_cause_analysis": "root_cause_analysis",
        "resolution_recommendations": "resolution_recommendations",
        "feedback_integration": "feedback_integration",
        "resolution_loop_status": "resolution_loop_status",
    }

    @staticmethod
    def initialize_workflow_state(
        session_state: Dict[str, Any], incident_id: str
    ) -> None:
        """
        Initialize the workflow state for a new incident resolution session.

        Args:
            session_state: The session state dictionary
            incident_id: The incident identifier
        """
        workflow_state = WorkflowState(
            current_phase=ResolutionPhase.INITIAL_ANALYSIS,
            phase_start_time=datetime.now(),
        )

        session_state[EnhancedStateManager.WORKFLOW_STATE_KEY] = (
            workflow_state.to_dict()
        )
        session_state["incident_id"] = incident_id
        session_state["workflow_initialized"] = True
        session_state["workflow_start_time"] = datetime.now().isoformat()

    @staticmethod
    def get_workflow_state(session_state: Dict[str, Any]) -> Optional[WorkflowState]:
        """
        Get the current workflow state.

        Args:
            session_state: The session state dictionary

        Returns:
            WorkflowState object or None if not initialized
        """
        state_data = session_state.get(EnhancedStateManager.WORKFLOW_STATE_KEY)
        if state_data:
            return WorkflowState.from_dict(state_data)
        return None

    @staticmethod
    def update_workflow_phase(
        session_state: Dict[str, Any], new_phase: ResolutionPhase
    ) -> None:
        """
        Update the current workflow phase.

        Args:
            session_state: The session state dictionary
            new_phase: The new phase to transition to
        """
        workflow_state = EnhancedStateManager.get_workflow_state(session_state)
        if workflow_state:
            # Mark current phase as completed
            if workflow_state.current_phase not in workflow_state.completed_phases:
                workflow_state.completed_phases.append(workflow_state.current_phase)

            # Update to new phase
            workflow_state.current_phase = new_phase
            workflow_state.phase_start_time = datetime.now()

            session_state[EnhancedStateManager.WORKFLOW_STATE_KEY] = (
                workflow_state.to_dict()
            )

    @staticmethod
    def get_incident_context(
        session_state: Dict[str, Any],
    ) -> Optional[IncidentContext]:
        """
        Get the incident context from state.

        Args:
            session_state: The session state dictionary

        Returns:
            IncidentContext object or None if not available
        """
        context_data = session_state.get(EnhancedStateManager.INCIDENT_CONTEXT_KEY)
        if context_data:
            return IncidentContext.from_dict(context_data)
        return None

    @staticmethod
    def set_incident_context(
        session_state: Dict[str, Any], context: IncidentContext
    ) -> None:
        """
        Set the incident context in state.

        Args:
            session_state: The session state dictionary
            context: The IncidentContext object to store
        """
        session_state[EnhancedStateManager.INCIDENT_CONTEXT_KEY] = context.to_dict()

    @staticmethod
    def get_phase_data(
        session_state: Dict[str, Any], phase: ResolutionPhase
    ) -> Dict[str, Any]:
        """
        Get all data for a specific workflow phase.

        Args:
            session_state: The session state dictionary
            phase: The workflow phase to get data for

        Returns:
            Dictionary containing all data for the specified phase
        """
        if phase == ResolutionPhase.INITIAL_ANALYSIS:
            keys = EnhancedStateManager.INITIAL_ANALYSIS_KEYS
        elif phase == ResolutionPhase.DATA_GATHERING:
            keys = EnhancedStateManager.DATA_GATHERING_KEYS
        elif phase == ResolutionPhase.RESOLUTION_LOOP:
            keys = EnhancedStateManager.RESOLUTION_LOOP_KEYS
        else:
            return {}

        phase_data = {}
        for logical_key, state_key in keys.items():
            if state_key in session_state:
                phase_data[logical_key] = session_state[state_key]

        return phase_data

    @staticmethod
    def is_phase_complete(
        session_state: Dict[str, Any], phase: ResolutionPhase
    ) -> bool:
        """
        Check if a workflow phase has been completed.

        Args:
            session_state: The session state dictionary
            phase: The workflow phase to check

        Returns:
            True if the phase is complete, False otherwise
        """
        workflow_state = EnhancedStateManager.get_workflow_state(session_state)
        if workflow_state:
            return phase in workflow_state.completed_phases
        return False

    @staticmethod
    def get_workflow_summary(session_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of the current workflow state and progress.

        Args:
            session_state: The session state dictionary

        Returns:
            Dictionary containing workflow summary information
        """
        workflow_state = EnhancedStateManager.get_workflow_state(session_state)
        incident_context = EnhancedStateManager.get_incident_context(session_state)

        summary = {
            "workflow_initialized": session_state.get("workflow_initialized", False),
            "incident_id": session_state.get("incident_id"),
            "workflow_start_time": session_state.get("workflow_start_time"),
            "current_phase": None,
            "completed_phases": [],
            "iteration_count": 0,
            "incident_status": None,
        }

        if workflow_state:
            summary.update(
                {
                    "current_phase": workflow_state.current_phase.value,
                    "completed_phases": [
                        phase.value for phase in workflow_state.completed_phases
                    ],
                    "iteration_count": workflow_state.iteration_count,
                    "phase_start_time": workflow_state.phase_start_time.isoformat(),
                }
            )

        if incident_context:
            summary["incident_status"] = incident_context.status.value

        return summary
