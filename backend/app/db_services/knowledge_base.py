from uuid import UUID

from database.core import DbSession
from entities.knowledge_base import KnowledgeBase


def get_kb_entry_by_id(db: DbSession, entry_id: UUID):
    return db.query(KnowledgeBase).filter(KnowledgeBase.id == entry_id).first()


def get_kb_entry_by_project_id(db: DbSession, project_id: UUID):
    return (
        db.query(KnowledgeBase).filter(KnowledgeBase.project_id == project_id).first()
    )


def get_kb_entries(db: DbSession, offset: int = 0, limit: int = 10):
    query = db.query(KnowledgeBase)
    total = query.count()
    items = query.offset(offset).limit(limit).all()
    return items, total
