# Incident Management Connectors

This package provides connectors to integrate with various incident management systems (like Jira, ServiceNow, GitHub) and sync them with our incident management system. The connectors fetch issues and convert them into a standardized incident format, which is then processed by Celery tasks for asynchronous processing.

## Architecture Overview

### Core Components

#### 1. Connectors Module (`connectors/`)
- **Base Connector** (`base_connector.py`):
  - Abstract base class defining the interface for all connectors
  - Handles database operations and data transformation
  - Provides standardized methods for incident creation and updates
  - Manages database sessions and error handling

- **Specific Connectors** (e.g., `github_connector.py`):
  - Implements the BaseConnector interface for specific platforms
  - Handles platform-specific API interactions
  - Maps platform-specific data to our incident format
  - Uses AI (Gemini) for intelligent field mapping

#### 2. Celery Tasks (`tasks/`)
- **GitHub Tasks**:
  - `github_import`: Imports issues from a GitHub repository into the incident management system
  - `incident_sync`: Syncs GitHub issues with existing incidents, updating them as needed

- **Task Flow**:
  1. Connector fetches issues from external system (e.g., GitHub)
  2. Issues are converted to standardized incident format
  3. Celery tasks (`github_import`, `incident_sync`) process incidents asynchronously
  4. Results are stored in the database and task status is updated

#### 3. API Routes & Service Layer
- **Routes (`routes/jobs/controller.py`)**:
  - `/jobs/import_github`: Triggers the `github_import` Celery task to import issues from a GitHub repository
  - `/jobs/sync_github`: Triggers the `incident_sync` Celery task to sync issues with existing incidents
  - `/jobs/status/{job_id}`: Retrieves the status and result of a submitted job
- **Service Layer (`routes/jobs/service.py`)**:
  - Handles job creation, status tracking, and database operations for job records
  - Provides abstraction between API routes and database logic, ensuring clean separation of concerns

## Testing the System

### Prerequisites

- Ensure `GITHUB_ACCESS_TOKEN` is added in the `.env` file.
- Ensure all dependencies from `requirements.txt` are installed.
- Ensure a Celery worker service is defined in `docker-compose.yml`.

### How to Use

1. Access Swagger UI:
   - Open `http://localhost:8000/docs` in your browser
   - Authenticate using the admin credentials

2. Test Connector Sync:
   - Navigate to `/api/v1/connectors/sync` endpoint
   - Use the following payload:
   ```json
   {
     "connector_type": "github",
     "config": {
       "repo_name": "pytorch/pytorch",
       "since": "2024-01-01T00:00:00Z",
       "until": "2024-03-20T00:00:00Z"
     }
   }
   ```
   - This will trigger the Celery task for syncing

3. Monitor Task Status:
   - Use `/api/v1/tasks/{task_id}` endpoint
   - Check task status and results
   - View any errors or warnings

4. View Synced Incidents:
   - Use `/api/v1/incidents` endpoint
   - Filter and view the synced incidents
   - Verify the data mapping and formatting

### Task Status Codes

- `PENDING`: Task is waiting for execution
- `STARTED`: Task has been started
- `SUCCESS`: Task completed successfully
- `FAILURE`: Task failed with an error
- `RETRY`: Task is being retried

### Error Handling

- Failed tasks are automatically retried (configurable)
- Error details are logged and available via task status
- Connector errors are captured and reported in task results

## Configuration

### Connector Configuration
- Modify `config.py` for connector-specific settings
- Adjust batch sizes and retry policies
- Configure API rate limits and timeouts

### Celery Configuration
- Task retry policies
- Worker concurrency
- Result backend settings
- Task time limits

## Monitoring and Maintenance

1. Task Monitoring:
   - Use Flower UI (`http://localhost:5555`) for Celery monitoring
   - View task history and statistics
   - Monitor worker status

2. Logging:
   - Check application logs for connector operations
   - Monitor Celery worker logs
   - Review task execution logs

3. Maintenance:
   - Regular cleanup of completed tasks
   - Monitoring of task queue size
   - Health checks for connectors

# Connectors Documentation

## GitHub Connector

### Authentication
The GitHub connector supports two authentication methods:
1.  **Personal Access Token (PAT)**: A simple method for individual users to grant access. The connector will use this method if a `GITHUB_ACCESS_TOKEN` is found in the environment variables.
2.  **GitHub App**: The recommended method for organizations or for providing granular permissions. The connector falls back to this method if a PAT is not provided.

### Environment Variables
Based on your chosen authentication method, you will need to configure the following environment variables.

**For Personal Access Token (PAT) Authentication:**
```
GITHUB_ACCESS_TOKEN=your_github_personal_access_token
```

**For GitHub App Authentication:**
```
GITHUB_APP_ID=your_app_id
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----"
```
> **Note on `GITHUB_APP_PRIVATE_KEY`**: The private key should be enclosed in double quotes to ensure the newline characters are correctly parsed from the `.env` file.

### Setup Guide for Users

This guide is for end-users who need to connect their repositories to the application.

#### Method 1: Personal Access Token (PAT)
1. Go to GitHub Settings > Developer Settings > Personal Access Tokens > Tokens (classic).
2. Click "Generate new token (classic)".
3. **Recommended permissions:**
   - For public repositories: `public_repo` (sufficient for all operations on public repos)
   - For private repositories: `repo` (Full control of private repositories)
   - `read:org` (Read organization data, only if needed)
4. Set token expiration as needed.
5. Copy the generated token and add it to your `.env` file as `GITHUB_ACCESS_TOKEN`.

#### Method 2: Installing the GitHub App
To connect your repository, you need to install the application's GitHub App.
1.  Navigate to the public installation URL for the GitHub App (this will be provided by the application administrator).
2.  Click "Install" or "Configure".
3.  Select the account or organization where you want to install the app.
4.  Choose which repositories the app can access. You can select "All repositories" or "Only select repositories".
5.  Click "Install" to grant access. The application will now be able to access your selected repositories.

### Setup Guide for Developers (Creating the GitHub App)
This guide is for developers setting up the application for the first time.

1.  **Register a new GitHub App:**
    - Navigate to your organization's settings (or your personal account settings if you are not using an organization).
    - Go to **Developer settings** > **GitHub Apps**.
    - Click **New GitHub App**.

2.  **Fill in App Details:**
    - **GitHub App name**: A unique name for your application (e.g., "Incidoc AI").
    - **Homepage URL**: The URL of your application's homepage.

3.  **Set Permissions:**
    The application needs the following permissions to function correctly. Under **Repository permissions**:
    - **Issues**: `Read & write`. Required for reading issue data and posting comments.
    - **Contents**: `Read-only`. Required to read repository metadata like the README file.
    - **Metadata**: `Read-only` (Default).

4.  **Subscribe to Events (Optional but Recommended):**
    - You can subscribe to events like `Issues` to enable real-time updates in the future.

5.  **Create the App:**
    - Click **Create GitHub App**.

6.  **Retrieve Credentials:**
    - After creation, you'll be redirected to the app's settings page.
    - **App ID**: The `App ID` is displayed on the "General" settings page. Add this to your `.env` as `GITHUB_APP_ID`.
    - **Private Key**: Under "Private keys", click **Generate a private key**. A `.pem` file will be downloaded. Open this file, copy its entire contents, and add it to your `.env` file as `GITHUB_APP_PRIVATE_KEY`. Ensure it is formatted correctly as a multi-line string.

7.  **Make the App Public (Optional):**
    - If you want users outside your organization to install the app, you can make it public from the "Advanced" settings page.

### GitHub API Rate Limits
- Unauthenticated requests: 60 requests per hour
- Authenticated requests with PAT: 5,000 requests per hour
- Authenticated requests via a GitHub App on behalf of a user: 15,000 requests per hour for enterprise accounts.
- Rate limit reset: Every hour
- Rate limit headers in response:
  - `X-RateLimit-Limit`: Total requests allowed per hour
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets (Unix timestamp)

The connector implements rate limit handling:
- Checks remaining API calls before operations
- Warns when less than 20% of rate limit remains
- Stops operations when less than 10 calls remain
- Logs rate limit status and reset times

## LLM Connector (via LiteLLM)

### LLM Support
- The system now uses [LiteLLM](https://github.com/BerriAI/litellm), which provides a unified interface to a wide range of LLM providers and models (including OpenAI, Gemini, Azure, Anthropic, Cohere, and more).
- Any LLM supported by LiteLLM can be used as the backend for this connector.

### Features
- Flexible model selection via LiteLLM configuration
- Automatic handling of provider-specific rate limits and retries (as supported by LiteLLM)
- Unified API for prompt completion, chat, and other LLM tasks

### Configuration
- See the LiteLLM documentation for details on configuring providers, API keys, and model selection.

## Environment Variables
Required environment variables:
```
GITHUB_ACCESS_TOKEN=your_github_pat
GEMINI_API_KEY=your_gemini_api_key
```

## Error Handling
Both connectors implement comprehensive error handling:
- Rate limit errors
- Authentication errors
- Network errors
- Invalid response errors

All errors are logged with appropriate context for debugging.

## Primary rate limit for unauthenticated users
You can make unauthenticated requests if you are only fetching public data. Unauthenticated requests are associated with the originating IP address, not with the user or application that made the request.

The primary rate limit for unauthenticated requests is 60 requests per hour.

## Primary rate limit for authenticated users
You can use a personal access token to make API requests. Additionally, you can authorize a GitHub App or OAuth app, which can then make API requests on your behalf.

All of these requests count towards your personal rate limit of 5,000 requests per hour. Requests made on your behalf by a GitHub App that is owned by a GitHub Enterprise Cloud organization have a higher rate limit of 15,000 requests per hour. Similarly, requests made on your behalf by a OAuth app that is owned or approved by a GitHub Enterprise Cloud organization have a higher rate limit of 15,000 requests per hour if you are a member of the GitHub Enterprise Cloud organization.
