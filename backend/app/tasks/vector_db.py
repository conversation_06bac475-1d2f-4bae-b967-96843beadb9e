"""
Celery Tasks for Vector DB Operations
====================================

This module contains Celery tasks for asynchronous vector database operations.
These tasks handle embedding generation and vector DB upsert operations
for both incidents and documents in the background, ensuring that main operations remain non-blocking.
"""

from typing import Any, Dict
from uuid import UUID

from database.core import SessionLocal
from utils.celery_worker import celery_app
from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

# Initialize logger for vector DB tasks
logger = get_service_logger("vector_db_tasks")


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def upsert_incident_embedding_task(self, incident_id: str) -> Dict[str, Any]:
    """
    Async task to upsert incident embedding to vector DB.

    This task takes an incident ID, retrieves the incident data from the database,
    generates an embedding, and upserts it to the Qdrant incidents collection using
    the VectorSearchService directly. The upsert operation handles both creation of
    new incidents and updating of existing ones based on the incident ID. It includes
    retry logic for handling transient failures and comprehensive logging.

    Args:
        incident_id (str): The UUID string of the incident to process

    Returns:
        Dict[str, Any]: Task result with status and incident_id

    Raises:
        Exception: Re-raised after max retries for permanent failures

    Security:
        - Never logs sensitive incident content
        - Handles all error cases gracefully
    """
    try:
        logger.info(f"Starting async embedding upsert task for incident {incident_id}")

        # Convert string ID to UUID
        incident_uuid = UUID(incident_id)

        # Get database session and vector search service
        db = SessionLocal()
        try:
            vector_service = VectorSearchService()

            # Perform the upsert operation using the vector search service directly
            success = vector_service.upsert_incident(db, incident_uuid)
        finally:
            db.close()

        if not success:
            raise Exception("Upsert operation failed - service returned False")

        logger.info(
            f"Successfully completed async embedding upsert for incident {incident_id}"
        )
        return {
            "status": "success",
            "incident_id": incident_id,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(f"Embedding upsert task failed for incident {incident_id}: {e}")

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)  # Exponential backoff
            logger.warning(
                f"Retrying embedding upsert task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error(
                f"Max retries exceeded for embedding upsert task for incident {incident_id}"
            )
            raise


@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def upsert_document_embedding_task(self, document_id: str) -> Dict[str, Any]:
    """
    Async task to upsert document embedding to vector DB.

    This task takes a document ID, retrieves the document data from the database,
    generates an embedding, and upserts it to the Qdrant documents collection using
    the VectorSearchService directly. The upsert operation handles both creation of
    new documents and updating of existing ones based on the document ID. It includes
    retry logic for handling transient failures and comprehensive logging.

    Args:
        document_id (str): The UUID string of the document to process

    Returns:
        Dict[str, Any]: Task result with status and document_id

    Raises:
        Exception: Re-raised after max retries for permanent failures

    Security:
        - Never logs sensitive document content
        - Handles all error cases gracefully
    """
    try:
        logger.info(f"Starting async embedding upsert task for document {document_id}")

        # Convert string ID to UUID
        document_uuid = UUID(document_id)

        # Get database session and vector search service
        db = SessionLocal()
        try:
            vector_service = VectorSearchService()

            # Perform the upsert operation using the vector search service directly
            success = vector_service.upsert_document(db, document_uuid)
        finally:
            db.close()

        if not success:
            raise Exception("Upsert operation failed - service returned False")

        logger.info(
            f"Successfully completed async embedding upsert for document {document_id}"
        )
        return {
            "status": "success",
            "document_id": document_id,
            "task_id": self.request.id,
        }

    except Exception as e:
        logger.error(
            f"Document embedding upsert task failed for document {document_id}: {e}"
        )

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 60 * (2**self.request.retries)  # Exponential backoff
            logger.warning(
                f"Retrying document embedding upsert task in {countdown} seconds (attempt {self.request.retries + 1}/{self.max_retries})"
            )
            raise self.retry(countdown=countdown, exc=e)
        else:
            logger.error(
                f"Max retries exceeded for document embedding upsert task for document {document_id}"
            )
            raise
