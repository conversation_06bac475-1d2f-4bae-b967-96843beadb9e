import os
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StreamableHTTPConnectionParams,
)
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from dotenv import load_dotenv

# Load environment variables from a .env file (recommended for API keys)
load_dotenv()

# Create session service
session_service = InMemorySessionService()


async def get_or_create_session(
    app_name: str, user_id: str, session_id: str, initial_state: dict
):
    """Get or create a session for the agent."""
    retrieved_session = await session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if retrieved_session:
        return retrieved_session
    return await session_service.create_session(
        app_name=app_name, user_id=user_id, session_id=session_id, state=initial_state
    )


async def main():
    # Define constants for identifying the interaction context
    APP_NAME = "github_app"
    USER_ID = "user_1"
    SESSION_ID = "session_001"

    # Get your GitHub Personal Access Token from environment variables
    github_token = "****************************************"
    if not github_token:
        raise ValueError(
            "GITHUB_TOKEN environment variable not set. Please set your GitHub Personal Access Token."
        )

    # Configure the connection to the remote GitHub MCP server
    # The URL for the official GitHub MCP server might be something like:
    # "https://api.githubcopilot.com/mcp/" or similar.
    # It's always best to check the latest documentation for the exact URL.
    # For this example, let's assume a common pattern for MCP servers
    # and how authentication is passed.
    # The exact URL and header structure should be confirmed from official GitHub MCP server docs.
    github_mcp_url = "https://api.githubcopilot.com/mcp/"  # This is a common pattern, verify with GitHub's docs
    headers = {"Authorization": f"Bearer {github_token}"}

    # Create the MCPToolset, connecting to the remote server via HTTP/SSE
    try:
        github_mcp_tools = MCPToolset(
            connection_params=StreamableHTTPConnectionParams(
                url=github_mcp_url,
                headers=headers,
                # Set to True if the server supports Server-Sent Events (SSE) for streaming responses
                supports_sse=True,
            ),
            auth_credential=github_token,
            # Optional: Filter which tools to expose if the server has many and you only need a few
            # tool_filter=["list_repositories", "create_issue", "get_pull_request_status"]
        )
    except Exception as e:
        raise ValueError(f"Failed to connect to GitHub MCP server: {e}")

    AGENT_MODEL = "gemini/gemini-2.0-flash"  # Use the appropriate model for your agent
    # Define your ADK agent
    github_agent = Agent(
        name="github_assistant",
        model=LiteLlm(AGENT_MODEL),
        instruction="""
        You are a helpful GitHub assistant. You can perform actions on GitHub repositories.
        Use the available tools to:
        - List repositories.
        - Create issues.
        - Get the status of pull requests.
        - Provide code snippets or instructions if the user asks for a task you can't directly perform with your tools.
        Always be polite and clear in your responses.
        """,
        tools=[github_mcp_tools],
    )

    # Run the agent in an interactive session
    # Initialize session state
    initial_state = {"user:preferences": {"language": "English"}}
    await get_or_create_session(APP_NAME, USER_ID, SESSION_ID, initial_state)

    runner = Runner(
        agent=github_agent,
        app_name=APP_NAME,
        session_service=session_service,
    )
    print(
        "GitHub Agent Ready. Type your queries (e.g., 'List my repositories', 'Create an issue titled \"Bug in login\" in my-repo', 'What is the status of PR #123 in my-repo?'). Type 'exit' to quit."
    )

    while True:
        try:
            user_query = input("\nYou: ")
            if user_query.lower() == "exit":
                break

            # Create content for the message
            content = types.Content(role="user", parts=[types.Part(text=user_query)])

            # Get final response from agent
            final_response_text = "Agent did not produce a final response."

            async for event in runner.run_async(
                user_id=USER_ID, session_id=SESSION_ID, new_message=content
            ):
                if event.is_final_response():
                    if event.content and event.content.parts:
                        final_response_text = event.content.parts[0].text
                    elif event.actions and event.actions.escalate:
                        final_response_text = f"Agent escalated: {event.error_message or 'No specific message.'}"
                    break

            print(f"Agent: {final_response_text}")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            print("Please try again or type 'exit' to quit.")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
