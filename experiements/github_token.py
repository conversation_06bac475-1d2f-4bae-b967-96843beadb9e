import os
if __name__ == "__main__":
    from github import Github
    # A<PERSON>i (Personal) Personal Access Token = ****************************************
    # Razeeen PAT: ****************************************


    github = Github("****************************************")
    user = github.get_user()
    print(f"Authenticated as GitHub user: {user.login}")
    rate_limit = github.get_rate_limit()
    print(
        f"Rate limit: {rate_limit.core.remaining}/{rate_limit.core.limit} resets at {rate_limit.core.reset}"
    )