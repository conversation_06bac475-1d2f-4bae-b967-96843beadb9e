# Product Requirements Document (PRD): GitHub Integration via App & PAT

## Objective
Enable users to connect their GitHub repositories to Incidoc using the Incidoc GitHub App, with two authentication options:
- **A. Installing the Incidoc GitHub App** on their GitHub account/org/repo.
- **B. Providing a GitHub Personal Access Token (PAT)**, which will be used by the Incidoc GitHub App to authenticate as the user, not for direct API access.

**Note:** All GitHub API access is performed via the Incidoc GitHub App. If a PAT is provided, it is used by the app to authenticate and act on behalf of the user, not for direct access.

---

## GitHub App Setup Steps

1. **Create a GitHub App**
   - Go to [GitHub Developer Settings > GitHub Apps](https://github.com/settings/apps).
   - Click "New GitHub App".
   - Set the app name, description, and homepage URL (e.g., your Incidoc instance).
   - Set the callback URL (e.g., `https://your-instances.com/api/github/auth/callback`).
   - Set permissions required by Incidoc (e.g., repository contents, issues, pull requests, metadata, etc.).
   - Choose whether the app is for your account or an organization.
   - Enable "Request user authorization (OAuth) during installation" if you want to support user-level access.
   - Save the app.
2. **Generate and Download the Private Key**
   - In the app settings, generate a private key and download it. Store it securely (e.g., as an environment variable or in a secret manager).
3. **Note the App Credentials**
   - Copy the App ID, Client ID, and Client Secret. Store them securely for backend configuration.
4. **Install the App**
   - Use the installation URL to install the app on the desired user/org/repo.
5. **Configure Webhooks (Optional)**
   - Set up webhooks for events you want Incidoc to respond to (e.g., push, pull_request, issues).
6. **Update Incidoc Backend Configuration**
   - Add the private key, App ID, Client ID, and Client Secret to your backend environment/configuration.

---

## User Stories
- As a user, I want to connect my GitHub account to Incidoc using either a GitHub App installation or by providing a PAT, but in both cases, all actions are performed via the Incidoc GitHub App.
- As a user, I want to see which method I am currently using and be able to switch between them.
- As an admin, I want to ensure that users have authorized access to the correct repositories through the app.

## Acceptance Criteria
- Users can choose between GitHub App installation and providing a PAT.
- The backend always uses the Incidoc GitHub App for all GitHub API access.
- If a PAT is provided, it is used by the app to authenticate as the user.
- The UI clearly presents both options and their status.
- Secure storage and handling of PATs.
- The system can detect and display which repos the app (using the user's PAT if provided) has access to.

---

## Implementation Plan

### A. Backend Changes

#### Files to be Changed & Required Changes

**`app/connectors/github/github_connector.py`**
- Refactor the connector to support GitHub App authentication:
  - Implement logic to generate a JWT using the app's private key and App ID.
  - Use the JWT to request an installation access token for the user's installation (if using the app install flow).
  - If the user provides a PAT, use the PAT to authenticate the app as the user (using the GitHub App's OAuth flow if required), but always operate in the context of the app.
  - Add logic to select the correct authentication flow based on the config (installation ID, PAT, etc.).
  - Ensure all PyGithub client initialization uses the correct token (installation token or PAT-as-app).
  - Handle token refresh and error cases for both flows.
  - Update or add helper methods for these authentication steps.

**`app/tasks/github.py`**
- Update how the config is constructed and passed to `GitHubConnector`:
  - If using the app installation flow, ensure the installation ID (and any other required info) is included in the config.
  - If using the PAT flow, ensure the PAT is included in the config.
  - Remove any logic that directly handles authentication; all authentication should be handled in `GitHubConnector`.
  - The tasks should only be responsible for passing the correct config and calling connector methods.

- Review and update any other Celery tasks that interact with GitHub to ensure they use the new config structure and do not handle authentication directly.

- No changes are needed in agent-related files.

- Update database logic as needed to store installation IDs and PATs securely.

---

### B. GitHub Authentication API Endpoints

The following backend API endpoints are required to support GitHub App and PAT authentication. The frontend will use these endpoints to select the authentication type, initiate app installation, and submit a PAT if needed.

- **POST `/api/github/auth/initiate`**
  - Purpose: Returns the GitHub App installation URL and any instructions needed to start the installation flow.
- **POST `/api/github/auth/pat`**
  - Purpose: Accepts and securely stores a user's PAT, and uses it to authenticate the app as the user.
- **GET `/api/github/auth/status`**
  - Purpose: Returns the current authentication method (App or PAT), and lists accessible repositories.
- **GET `/api/github/auth/callback`**
  - Purpose: Handles the callback from GitHub after app installation, stores the installation ID for the user, and completes the authentication process.
- **DELETE `/api/github/auth`**
  - Purpose: Disconnects GitHub from Incidoc for the user, removing stored credentials and tokens.

---

### C. Frontend/UI Changes

#### 1. Settings/Integration Page
- **Option Selector**: UI for user to choose between "Install GitHub App" or "Provide PAT".
- **GitHub App Flow**:
  - Button: "Install GitHub App"
  - Calls `POST /api/github/auth/initiate` to get the installation URL, then redirects the user.
  - On return, the backend handles `/api/github/auth/callback` and the UI polls or fetches `/api/github/auth/status` to show success and accessible repos.
- **PAT Flow**:
  - Secure input for PAT.
  - Button: "Connect with PAT"
  - Calls `POST /api/github/auth/pat` to submit the PAT.
  - On success, the UI fetches `/api/github/auth/status` to show accessible repos.
- **Status Display**: Show which method is active, and allow switching.
- **Disconnect Button**: Allow user to disconnect GitHub by calling `DELETE /api/github/auth`.

#### 2. Error Handling
- Show clear errors if the app (even with the PAT) does not have access to the required repos.

#### Frontend Implementation Steps
1. Add a new "GitHub Integration" section in user/org settings.
2. Implement UI for selecting auth method (App install or PAT).
3. Implement GitHub App installation flow (redirect, callback handling, and status polling).
4. Implement PAT input and connection flow (with app context).
5. Display current status and allow switching/disconnecting.
6. Add error and success messages.

---

### D. DevOps
1. Store GitHub App private key, App ID, Client ID, and Client Secret securely (e.g., environment variable or secret manager).
2. Update documentation for setup and configuration.

---

## User Flow (Incidoc User Experience)

1. User navigates to "GitHub Integration" in Incidoc.
2. User is presented with two options:
   - **A. Install GitHub App**
     - Clicks "Install GitHub App".
     - Redirected to GitHub to install the app on their account/org/repo.
     - Redirected back to Incidoc, which confirms installation and shows accessible repos (via the app).
   - **B. Provide PAT**
     - Enters their GitHub PAT in a secure input.
     - Clicks "Connect with PAT".
     - Incidoc uses the PAT to authenticate the app as the user, and shows accessible repos (via the app).
3. User can see which method is active, and can disconnect or switch at any time.
4. All GitHub API actions in Incidoc use the Incidoc GitHub App, either with installation tokens or with the user's PAT for authentication.

---

## References
- [GitHub Apps authentication docs](https://docs.github.com/en/apps/creating-github-apps/authenticating-with-a-github-app)
- [PyGithub authentication](https://pygithub.readthedocs.io/en/latest/introduction.html#authentication)
