# Agents

## Requirements

We use Google Agent Development Kit to create agents.

## Debugging

To debug agents, go to backend folder by running `cd backend`

Use this command to run agents standalone in cli
`uv run adk run app/agents`

To use web interface, run
`uv run adk web app`

TO use adk server, run
`uv run adk api_server app`

## Limitations

See [Official Docs](https://google.github.io/adk-docs/tools/built-in-tools/#use-built-in-tools-with-other-tools)
Only one built-in tool is supported for each agent. No other tools of any type can be used in the same agent
```root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[custom_function],
    executor=[BuiltInCodeExecutor] # <-- not supported when used with tools
)
```

Built-in tools cannot be used within a sub-agent.
```
search_agent = Agent(
    model='gemini-2.0-flash',
    name='SearchAgent',
    instruction="""
    You're a specialist in Google Search
    """,
    tools=[google_search],
)
coding_agent = Agent(
    model='gemini-2.0-flash',
    name='CodeAgent',
    instruction="""
    You're a specialist in Code Execution
    """,
    executor=[BuiltInCodeExecutor],
)
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    sub_agents=[
        search_agent, # <-- not supported when agents that has builtin tools, are used as sub_agents
        coding_agent
    ],
)

root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[agent_tool.AgentTool(agent=search_agent), agent_tool.AgentTool(agent=coding_agent)], # <-- supported when builtin tools are used as tools
)
```
